﻿
# game/indepth_text.rpy:22
translate ukrainian a_label_8d79d234:

    # e "You just clicked to jump to a label."
    e "Ви щойно клацнули, щоб перейти до мітки."

# game/indepth_text.rpy:28
translate ukrainian text_578c4060:

    # e "Sometimes, when showing text, we'll want to change the way some of the text is displayed."
    e "Іноді під час показу тексту нам потрібно змінити спосіб відображення частини тексту."

# game/indepth_text.rpy:31
translate ukrainian text_60750345:

    # e "For example, we might want to have text that is {b}bold{/b}, {i}italic{/i}, {s}struckthrough{/s}, or {u}underlined{/u}."
    e "Наприклад, ми можемо захотіти мати текст {b}жирним{/b}, {i}курсивом{/i}, {s}закресленим{/s} або {u}підкресленим{/u}."

# game/indepth_text.rpy:33
translate ukrainian text_5e1a6ee8:

    # e "That's what text tags are for."
    e "Ось для чого призначені текстові теги."

# game/indepth_text.rpy:37
translate ukrainian text_38c63ec8:

    # e "Text tags are contained in braces, like the {{b} tag above. When a text tag takes a closing tag, the closing tag begins with a slash, like {{/b} does."
    e "Текстові теги містяться в дужках, як тег {{b} вище. Коли текстовий тег приймає закриваючий тег, закриваючий тег починається з скісної риски, як це робить {{/b}."

# game/indepth_text.rpy:39
translate ukrainian text_1760f9c8:

    # e "We've already seen the b, i, s, and u tags, but there are lot more than those. I'll show you the rest of them."
    e "Ми вже бачили теги b, i, s та u, але їх набагато більше. Я покажу вам решту."

# game/indepth_text.rpy:43
translate ukrainian text_a620251f:

    # e "The a text tag can {a=https://www.renpy.org}link to a website{/a} or {a=jump:a_label}jump to a label{/a}."
    e "Текстовий тег може {a=https://www.renpy.org}посилатися на сайт{/a} або {a=jump:a_label}переходити до мітки{/a}."

# game/indepth_text.rpy:49
translate ukrainian after_a_label_d22d5f4a:

    # e "The alpha text tag makes text {alpha=.5}translucent{/alpha}."
    e "Тег тексту 'alpha' робить текст {alpha=.5}прозорим{/alpha}."

# game/indepth_text.rpy:53
translate ukrainian after_a_label_7c2c3cd2:

    # e "The color text tag changes the {color=#0080c0}color{/color} of the text."
    e "Тег тексту 'color' змінює {color=#0080c0}кольор{/color} тексту."

# game/indepth_text.rpy:57
translate ukrainian after_a_label_3f81fe7b:

    # e "The cps text tag {cps=25}makes text type itself out slowly{/cps}, even if slow text is off."
    e "Тег тексту 'cps' {cps=25}повільно вводить текст{/cps}, навіть якщо повільний текст вимкнено."

# game/indepth_text.rpy:59
translate ukrainian after_a_label_b102941f:

    # e "The cps tag can also be relative to the default speed, {cps=*2}doubling{/cps} or {cps=*0.5}halving{/cps} it."
    e "Тег 'cps' також може бути відносною швидкості за умовчанням, {cps=*2}подвоєння{/cps} або {cps=*0.5}вдвічі{/cps}."

# game/indepth_text.rpy:64
translate ukrainian after_a_label_22c4339a:

    # e "The font tag changes the font, for example to {font=DejaVuSans-Bold.ttf}DejaVuSans-Bold.ttf{/font}."
    e "Тег 'font' змінює шрифт, наприклад на {font=DejaVuSans-Bold.ttf}DejaVuSans-Bold.ttf{/font}."

# game/indepth_text.rpy:66
translate ukrainian after_a_label_d43417d7:

    # e "Sometimes, changing to a bold font looks better than using the {{b} tag."
    e "Іноді зміна на жирний шрифт виглядає краще, ніж використання тегу {{b}."

# game/indepth_text.rpy:71
translate ukrainian after_a_label_f24052f9:

    # e "The k tag changes kerning. It can space the letters of a word {k=-.5}closer together{/k} or {k=.5}farther apart{/k}."
    e "Тег 'k' змінює кернінг. Він може розмістити літери слова на {k=-.5}ближче один до одного{/k} або {k=.5}дальше{/k}."

# game/indepth_text.rpy:76
translate ukrainian after_a_label_2310b922:

    # e "The size tag changes the size of text. It can make text {size=+10}bigger{/size} or {size=-10}smaller{/size}, or set it to a {size=30}fixed size{/size}."
    e "Тег 'size' змінює розмір тексту. Він може зробити текст {size=+10}більшим{/size} або {size=-10}меншим{/size} або встановити для нього {size=30}фіксований розмір{/size}."

# game/indepth_text.rpy:81
translate ukrainian after_a_label_f566abf2:

    # e "The space tag {space=30} adds horizontal space in text.{vspace=30}The vspace tag adds vertical space between lines."
    e "Тег 'space' {space=30} додає горизонтальний пробіл у тексті.{vspace=30}Тег 'vspace' додає вертикальний пробіл між рядками."

# game/indepth_text.rpy:85
translate ukrainian after_a_label_054b9ffa:

    # e "There are a few text tags that only makes sense in dialogue."
    e "Є кілька текстових тегів, які мають сенс лише в діалозі."

# game/indepth_text.rpy:89
translate ukrainian after_a_label_86efc45b:

    # e "The p tag breaks a paragraph,{p}and waits for the player to click."
    e "Тег 'p' розриває абзац{p}і чекає, поки гравець клацне."

# game/indepth_text.rpy:91
translate ukrainian after_a_label_3ece2387:

    # e "If it is given a number as an argument,{p=1.5}it waits that many seconds."
    e "Якщо йому надати число як аргумент, {p=1.5}він чекає стільки секунд."

# game/indepth_text.rpy:95
translate ukrainian after_a_label_3881f72d:

    # e "The w tag also waits for a click,{w} except it doesn't break lines,{w=.5} the way p does."
    e "Тег 'w' також очікує на клацання, {w} за винятком того, що він не розриває рядки, {w=.5} як це робить 'p'."

# game/indepth_text.rpy:100
translate ukrainian after_a_label_e5321e79:

    # eslow "The nw tag causes Ren'Py to continue past slow text,{nw}"
    eslow "Тег 'nw' змушує Ren'Py продовжувати роботу після повільного тексту,{nw}"

# game/indepth_text.rpy:102
translate ukrainian after_a_label_1f2697ba:

    # extend " to the next statement."
    extend " до наступного твердження."

# game/indepth_text.rpy:106
translate ukrainian after_a_label_dbfca166:

    # e "To break a line without pausing,\none can write \\n. \\' and \\\" include quotes in the text."
    e "Щоб розірвати рядок без паузи,\ніхто не може писати \\n. \\' і \\\" взяти в текст лапки."

# game/indepth_text.rpy:111
translate ukrainian after_a_label_ffdf7e76:

    # e "The interpolation feature takes a variable name in square brackets, and inserts it into text."
    e "Функція інтерполяції бере назву змінної в квадратних дужках і вставляє її в текст."

# game/indepth_text.rpy:117
translate ukrainian after_a_label_fc99fcbf:

    # e "For example, this displays the [variable!t]."
    e "Наприклад, це відображає [variable!t]."

# game/indepth_text.rpy:121
translate ukrainian after_a_label_c84d9087:

    # e "When the variable name is followed by !q, special characters are quoted. This displays the raw [variable!q!t], including the italics tags."
    e "Коли після імені змінної стоїть !q, спеціальні символи беруться в лапки. Це відображає необроблену [variable!q!t], включаючи теги курсиву."

# game/indepth_text.rpy:126
translate ukrainian after_a_label_c90f24a8:

    # e "When the variable name is followed by !t, it is translated to [variable!t]. It could be something else in a different language."
    e "Коли після імені змінної стоїть !t, воно перекладається як [variable!t]. Це може бути щось інше іншою мовою."

# game/indepth_text.rpy:129
translate ukrainian after_a_label_fb106a95:

    # e "Finally, certain characters are special. [[, {{, and \\ need to be doubled if included in text. The %% character should be doubled if used in dialogue."
    e "Нарешті, деякі символи є особливими. [[, {{ і \\ потрібно подвоїти, якщо вони включені в текст. Символ %% слід подвоювати, якщо він використовується в діалозі."

translate ukrainian strings:

    # game/indepth_text.rpy:115
    old "{i}variable value{/i}"
    new "{i}значення змінної{/i}"

    # game/indepth_text.rpy:124
    old "translatable text"
    new "перекладний текст"

