﻿
# game/tutorial_video.rpy:10
translate ukrainian tutorial_video_f34a17f5:

    # e "Ren'<PERSON><PERSON> supports playing movies. There are two ways of doing this."
    e "Ren'Py підтримує відтворення відео. Є два способи зробити це."

# game/tutorial_video.rpy:12
translate ukrainian tutorial_video_4aefd413:

    # e "The first way allows you to show a movie as an image, along with every other image that's displayed on the screen."
    e "Перший спосіб дозволяє показувати відео як зображення разом із усіма іншими зображеннями, які відображаються на екрані."

# game/tutorial_video.rpy:16
translate ukrainian tutorial_video_b56ccf19:

    # e "To do this, we first have to define an image to be a Movie displayable. Movie displayables take a movie to play, and can be given position properties."
    e "Щоб зробити це, ми спочатку маємо визначити зображення, яке можна відобразити у відео. Відображувані елементи відео беруть відео для відтворення та можуть надавати властивості позиції."

# game/tutorial_video.rpy:25
translate ukrainian tutorial_video_fbaa71e4:

    # e "Then, we can show the movie displayable, which starts the movie playing."
    e "Потім ми можемо відобразити відео, який починає його відтворення."

# game/tutorial_video.rpy:30
translate ukrainian tutorial_video_bbbb242d:

    # e "When we no longer want to play the movie, we can hide it."
    e "Коли ми більше не хочемо відтворювати відео, ми можемо його приховати."

# game/tutorial_video.rpy:34
translate ukrainian tutorial_video_a66b154c:

    # e "The other way to show a movie is with the renpy.movie_cutscene python function. This shows the movie fullscreen, either until it ends or until the user clicks."
    e "Інший спосіб показати відео — за допомогою функції python renpy.movie_cutscene. Це показує відео на весь екран, доки він не закінчиться або поки користувач не клацне."

# game/tutorial_video.rpy:41
translate ukrainian tutorial_video_63e75209:

    # e "A Movie displayable can also take a mask with an alpha channel, which lets you make movie sprites. But that's more complicated, so I'll stop here for now."
    e "Відображуване відео також може приймати маску з альфа-каналом, що дозволяє створювати спрайти відео. Але це складніше, тому я наразі зупинюся на цьому."

