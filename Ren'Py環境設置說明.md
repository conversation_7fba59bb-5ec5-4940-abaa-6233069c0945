# Ren'Py 環境設置說明
## 隔牆的溫柔 - 開發環境配置

---

## ✅ 已完成的設置

### 1. Ren'Py SDK 下載與安裝
- **版本**：Ren'Py 8.4.1 "Tomorrowland"
- **發布日期**：2025年7月24日
- **檔案大小**：152 MB (ZIP格式)
- **安裝位置**：`./renpy-8.4.1-sdk/`

### 2. 項目結構建立
```
隔牆的溫柔/
├── renpy-8.4.1-sdk/        # Ren'Py SDK
├── game/                   # 遊戲主要檔案
│   ├── options.rpy         # 遊戲基本設定
│   ├── script.rpy          # 主劇本檔案
│   ├── gui.rpy            # GUI介面設定
│   ├── screens.rpy        # 螢幕界面定義
│   └── gui/               # GUI資源資料夾
├── images/                # 圖片資源
└── 企劃文檔/              # 企劃相關文檔
```

### 3. 核心檔案配置

#### 📄 game/options.rpy - 遊戲基本設定
- 遊戲名稱：隔牆的溫柔
- 版本：v1.0
- 解析度：1920x1080
- 成人內容設定
- 字體與語言配置

#### 📄 game/script.rpy - 主劇本
- 角色定義（美咲、男主角）
- 成人內容警告系統
- 第一章完整劇本
- 選擇分支系統
- 好感度系統

#### 📄 game/gui.rpy - 介面設計
- 顏色主題設定
- 字體配置
- 按鈕樣式
- 對話框設計

#### 📄 game/screens.rpy - 使用者介面
- 主選單螢幕
- 遊戲選單系統
- 存檔/載入介面
- 設定螢幕
- 成人內容警告螢幕

---

## 🚀 啟動方式

### 方法1：使用Ren'Py Launcher
1. 進入 `renpy-8.4.1-sdk` 資料夾
2. 執行 `renpy.exe launcher`
3. 在Launcher中選擇項目資料夾
4. 點擊「Launch Project」

### 方法2：直接啟動
1. 進入 `renpy-8.4.1-sdk` 資料夾
2. 執行 `renpy.exe "項目路徑"`

### 方法3：開發模式
1. 在項目根目錄執行：
```bash
cd renpy-8.4.1-sdk
renpy.exe launcher
```

---

## 🎮 當前遊戲功能

### ✅ 已實現功能
- **成人內容警告**：18歲年齡確認
- **角色系統**：美咲與男主角定義
- **第一章劇情**：深夜邂逅完整流程
- **選擇分支**：3個不同選擇路線
- **好感度系統**：根據選擇累積好感度
- **基本UI**：主選單、遊戲選單、設定等

### 🔄 部分實現功能
- **第二章劇情**：框架已建立，內容待完善
- **圖片系統**：資料夾結構已建立，圖片待生成
- **音效系統**：設定已配置，音檔待添加

### ⏳ 待實現功能
- 完整5章劇情內容
- 角色立繪與CG圖片
- 背景音樂與音效
- 存檔縮圖功能
- 成人CG場景

---

## 🛠️ 開發工具配置

### 推薦編輯器
- **VS Code**：配合Ren'Py Language擴展
- **Atom**：支援Ren'Py語法高亮
- **內建編輯器**：Ren'Py Launcher內建

### 除錯工具
- **開發者控制台**：遊戲中按 `Shift+O`
- **重新載入**：遊戲中按 `Shift+R`
- **變數檢視器**：開發者控制台中使用

### 測試功能
- **快速測試**：Launcher中的「Launch Project」
- **建置測試**：Build Distributions功能
- **多平台測試**：Windows/Mac/Linux支援

---

## 📋 下一步開發計劃

### 優先級1：內容完善
1. **完成劇本**：第2-5章詳細劇情
2. **角色立繪**：使用Stable Diffusion生成
3. **背景圖片**：場景背景製作

### 優先級2：功能增強
1. **音效整合**：背景音樂與音效
2. **CG場景**：成人內容圖片
3. **UI美化**：自定義界面設計

### 優先級3：優化與發布
1. **性能優化**：載入速度與記憶體使用
2. **多語言支援**：英文版本製作
3. **發布準備**：打包與分發

---

## ⚠️ 注意事項

### 開發環境
- **Python版本**：Ren'Py 8.4使用Python 3.12
- **系統需求**：Windows 10或更新版本
- **記憶體需求**：建議4GB以上RAM

### 成人內容處理
- 確保所有成人內容都有適當警告
- 遵守當地法律法規
- 設置年齡驗證機制

### 版本控制
- 建議使用Git進行版本管理
- 定期備份項目檔案
- 保留開發過程記錄

---

## 🔧 常見問題解決

### 啟動問題
- 確認renpy.exe檔案存在
- 檢查項目路徑是否正確
- 確認game資料夾結構完整

### 編碼問題
- 使用UTF-8編碼保存所有.rpy檔案
- 中文字符顯示異常時檢查字體設定

### 圖片載入問題
- 確認圖片路徑正確
- 支援格式：PNG, JPG, WEBP
- 建議使用PNG格式以支援透明背景

---

*環境設置版本：v1.0*
*最後更新：2025-07-27*
