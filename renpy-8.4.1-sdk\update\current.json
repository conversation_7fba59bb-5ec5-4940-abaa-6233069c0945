{"sdk": {"version": "07553e70b6e68c83b79e9b41fe6d039f5f906b18cf0af93907887130e2effcd9", "base_name": "renpy-8.4.1", "files": ["LICENSE.txt", "doc/3dstage.html", "doc/_images/axes_3d_1.png", "doc/_images/axes_3d_2.png", "doc/_images/axes_3d_3.png", "doc/_images/borders.png", "doc/_images/borders1.png", "doc/_images/borders2.png", "doc/_images/borders3.png", "doc/_images/frame_example.png", "doc/_static/_sphinx_javascript_frameworks_compat.js", "doc/_static/ajax-loader.gif", "doc/_static/basic.css", "doc/_static/bootstrap-2.3.2/css/bootstrap-responsive.css", "doc/_static/bootstrap-2.3.2/css/bootstrap-responsive.min.css", "doc/_static/bootstrap-2.3.2/css/bootstrap.css", "doc/_static/bootstrap-2.3.2/css/bootstrap.min.css", "doc/_static/bootstrap-2.3.2/img/glyphicons-halflings-white.png", "doc/_static/bootstrap-2.3.2/img/glyphicons-halflings.png", "doc/_static/bootstrap-2.3.2/js/bootstrap.js", "doc/_static/bootstrap-2.3.2/js/bootstrap.min.js", "doc/_static/bootstrap-3.3.6/css/bootstrap-theme.css", "doc/_static/bootstrap-3.3.6/css/bootstrap-theme.css.map", "doc/_static/bootstrap-3.3.6/css/bootstrap-theme.min.css", "doc/_static/bootstrap-3.3.6/css/bootstrap-theme.min.css.map", "doc/_static/bootstrap-3.3.6/css/bootstrap.css", "doc/_static/bootstrap-3.3.6/css/bootstrap.css.map", "doc/_static/bootstrap-3.3.6/css/bootstrap.min.css", "doc/_static/bootstrap-3.3.6/css/bootstrap.min.css.map", "doc/_static/bootstrap-3.3.6/fonts/glyphicons-halflings-regular.eot", "doc/_static/bootstrap-3.3.6/fonts/glyphicons-halflings-regular.svg", "doc/_static/bootstrap-3.3.6/fonts/glyphicons-halflings-regular.ttf", "doc/_static/bootstrap-3.3.6/fonts/glyphicons-halflings-regular.woff", "doc/_static/bootstrap-3.3.6/fonts/glyphicons-halflings-regular.woff2", "doc/_static/bootstrap-3.3.6/js/bootstrap.js", "doc/_static/bootstrap-3.3.6/js/bootstrap.min.js", "doc/_static/bootstrap-3.3.6/js/npm.js", "doc/_static/bootstrap-3.4.1/css/bootstrap-theme.css", "doc/_static/bootstrap-3.4.1/css/bootstrap-theme.css.map", "doc/_static/bootstrap-3.4.1/css/bootstrap-theme.min.css", "doc/_static/bootstrap-3.4.1/css/bootstrap-theme.min.css.map", "doc/_static/bootstrap-3.4.1/css/bootstrap.css", "doc/_static/bootstrap-3.4.1/css/bootstrap.css.map", "doc/_static/bootstrap-3.4.1/css/bootstrap.min.css", "doc/_static/bootstrap-3.4.1/css/bootstrap.min.css.map", "doc/_static/bootstrap-3.4.1/fonts/glyphicons-halflings-regular.eot", "doc/_static/bootstrap-3.4.1/fonts/glyphicons-halflings-regular.svg", "doc/_static/bootstrap-3.4.1/fonts/glyphicons-halflings-regular.ttf", "doc/_static/bootstrap-3.4.1/fonts/glyphicons-halflings-regular.woff", "doc/_static/bootstrap-3.4.1/fonts/glyphicons-halflings-regular.woff2", "doc/_static/bootstrap-3.4.1/js/bootstrap.js", "doc/_static/bootstrap-3.4.1/js/bootstrap.min.js", "doc/_static/bootstrap-3.4.1/js/npm.js", "doc/_static/bootstrap-sphinx.css", "doc/_static/bootstrap-sphinx.js", "doc/_static/bootswatch-2.3.2/amelia/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/cerulean/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/cosmo/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/cyborg/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/flatly/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/img/glyphicons-halflings-white.png", "doc/_static/bootswatch-2.3.2/img/glyphicons-halflings.png", "doc/_static/bootswatch-2.3.2/journal/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/readable/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/simplex/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/slate/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/spacelab/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/spruce/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/superhero/bootstrap.min.css", "doc/_static/bootswatch-2.3.2/united/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/cerulean/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/cosmo/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/custom/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/cyborg/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/darkly/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/flatly/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/fonts/glyphicons-halflings-regular.eot", "doc/_static/bootswatch-3.3.6/fonts/glyphicons-halflings-regular.svg", "doc/_static/bootswatch-3.3.6/fonts/glyphicons-halflings-regular.ttf", "doc/_static/bootswatch-3.3.6/fonts/glyphicons-halflings-regular.woff", "doc/_static/bootswatch-3.3.6/fonts/glyphicons-halflings-regular.woff2", "doc/_static/bootswatch-3.3.6/journal/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/lumen/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/paper/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/readable/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/sandstone/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/simplex/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/slate/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/spacelab/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/superhero/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/united/bootstrap.min.css", "doc/_static/bootswatch-3.3.6/yeti/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/cerulean/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/cosmo/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/cyborg/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/darkly/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/flatly/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/fonts/glyphicons-halflings-regular.eot", "doc/_static/bootswatch-3.4.1/fonts/glyphicons-halflings-regular.svg", "doc/_static/bootswatch-3.4.1/fonts/glyphicons-halflings-regular.ttf", "doc/_static/bootswatch-3.4.1/fonts/glyphicons-halflings-regular.woff", "doc/_static/bootswatch-3.4.1/fonts/glyphicons-halflings-regular.woff2", "doc/_static/bootswatch-3.4.1/journal/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/lumen/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/paper/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/readable/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/sandstone/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/simplex/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/slate/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/spacelab/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/superhero/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/united/bootstrap.min.css", "doc/_static/bootswatch-3.4.1/yeti/bootstrap.min.css", "doc/_static/comment-bright.png", "doc/_static/comment-close.png", "doc/_static/comment.png", "doc/_static/css/badge_only.css", "doc/_static/css/fonts/Roboto-Slab-Bold.woff", "doc/_static/css/fonts/Roboto-Slab-Bold.woff2", "doc/_static/css/fonts/Roboto-Slab-Regular.woff", "doc/_static/css/fonts/Roboto-Slab-Regular.woff2", "doc/_static/css/fonts/fontawesome-webfont.eot", "doc/_static/css/fonts/fontawesome-webfont.svg", "doc/_static/css/fonts/fontawesome-webfont.ttf", "doc/_static/css/fonts/fontawesome-webfont.woff", "doc/_static/css/fonts/fontawesome-webfont.woff2", "doc/_static/css/fonts/lato-bold-italic.woff", "doc/_static/css/fonts/lato-bold-italic.woff2", "doc/_static/css/fonts/lato-bold.woff", "doc/_static/css/fonts/lato-bold.woff2", "doc/_static/css/fonts/lato-normal-italic.woff", "doc/_static/css/fonts/lato-normal-italic.woff2", "doc/_static/css/fonts/lato-normal.woff", "doc/_static/css/fonts/lato-normal.woff2", "doc/_static/css/theme.css", "doc/_static/custom.css", "doc/_static/dark_mode_css/custom.css", "doc/_static/dark_mode_css/dark.css", "doc/_static/dark_mode_css/general.css", "doc/_static/dark_mode_js/default_dark.js", "doc/_static/dark_mode_js/default_light.js", "doc/_static/dark_mode_js/theme_switcher.js", "doc/_static/doctools.js", "doc/_static/documentation_options.js", "doc/_static/down-pressed.png", "doc/_static/down.png", "doc/_static/environment.txt", "doc/_static/file.png", "doc/_static/jquery-1.11.1.js", "doc/_static/jquery-3.1.0.js", "doc/_static/jquery-3.2.1.js", "doc/_static/jquery-3.5.1.js", "doc/_static/jquery-3.6.0.js", "doc/_static/jquery.js", "doc/_static/js/badge_only.js", "doc/_static/js/html5shiv-printshiv.min.js", "doc/_static/js/html5shiv.min.js", "doc/_static/js/jquery-1.11.0.min.js", "doc/_static/js/jquery-1.12.4.min.js", "doc/_static/js/jquery-fix.js", "doc/_static/js/theme.js", "doc/_static/language_data.js", "doc/_static/minus.png", "doc/_static/navbar-logo.png", "doc/_static/plus.png", "doc/_static/pygments.css", "doc/_static/renpydoc.css", "doc/_static/searchtools.js", "doc/_static/sphinx_highlight.js", "doc/_static/underscore-1.13.1.js", "doc/_static/underscore-1.3.1.js", "doc/_static/underscore.js", "doc/_static/up-pressed.png", "doc/_static/up.png", "doc/_static/websupport.js", "doc/achievement.html", "doc/android-packaging.html", "doc/android.html", "doc/atl.html", "doc/audio.html", "doc/audio_filters.html", "doc/bubble.html", "doc/build.html", "doc/cdd.html", "doc/cds.html", "doc/changelog.html", "doc/changelog6.html", "doc/character_callbacks.html", "doc/chromeos.html", "doc/cli.html", "doc/color_class.html", "doc/conditional.html", "doc/config.html", "doc/credits.html", "doc/custom_text_tags.html", "doc/developer_tools.html", "doc/dialogue.html", "doc/director.html", "doc/display_problems.html", "doc/displayables.html", "doc/displaying_images.html", "doc/distributor.html", "doc/downloader.html", "doc/drag_drop.html", "doc/editor.html", "doc/environment_variables.html", "doc/fetch.html", "doc/file_python.html", "doc/genindex.html", "doc/gesture.html", "doc/gui.html", "doc/gui_advanced.html", "doc/history.html", "doc/iap.html", "doc/im.html", "doc/incompatible.html", "doc/index.html", "doc/input.html", "doc/ios.html", "doc/keymap.html", "doc/label.html", "doc/language_basics.html", "doc/launcher.html", "doc/layeredimage.html", "doc/license.html", "doc/lifecycle.html", "doc/live2d.html", "doc/matrix.html", "doc/matrixcolor.html", "doc/menus.html", "doc/model.html", "doc/modes.html", "doc/mouse.html", "doc/movie.html", "doc/multiple.html", "doc/namespaces.html", "doc/nvl_mode.html", "doc/objects.inv", "doc/other.html", "doc/persistent.html", "doc/preferences.html", "doc/problems.html", "doc/py-function-class-index.html", "doc/python.html", "doc/quickstart.html", "doc/raspi.html", "doc/raspy.html", "doc/ren_py.html", "doc/reserved.html", "doc/rooms.html", "doc/save_load_rollback.html", "doc/screen_actions.html", "doc/screen_optimization.html", "doc/screen_python.html", "doc/screen_special.html", "doc/screens.html", "doc/screenshot.html", "doc/search.html", "doc/searchindex.js", "doc/security.html", "doc/self_voicing.html", "doc/shader_parts.html", "doc/side_image.html", "doc/skins.html", "doc/splashscreen_presplash.html", "doc/sponsors.html", "doc/sprites.html", "doc/statement_equivalents.html", "doc/std-style-property-index.html", "doc/std-transform-property-index.html", "doc/std-var-index.html", "doc/store_variables.html", "doc/style.html", "doc/style_properties.html", "doc/template_projects.html", "doc/text.html", "doc/textshaders.html", "doc/thequestion.html", "doc/thequestion_nvl.html", "doc/trans_trans_python.html", "doc/transform_properties.html", "doc/transforms.html", "doc/transitions.html", "doc/translating_renpy.html", "doc/translation.html", "doc/udd.html", "doc/updater.html", "doc/voice.html", "doc/web.html", "gui/game/gui.rpy", "gui/game/gui/bubble.png", "gui/game/gui/thoughtbubble.png", "gui/game/guisupport.rpy", "gui/game/libs/libs.txt", "gui/game/options.rpy", "gui/game/screens.rpy", "gui/game/script.rpy", "gui/game/testcases.rpy", "gui/gitignore.txt", "gui/project.json", "launcher/Atom.edit.py", "launcher/None.edit.py", "launcher/System Editor.edit.py", "launcher/Visual Studio Code (System).edit.py", "launcher/Visual Studio Code.edit.py", "launcher/game/ability.rpy", "launcher/game/ability.rpyc", "launcher/game/about.rpy", "launcher/game/about.rpyc", "launcher/game/add_file.rpy", "launcher/game/add_file.rpyc", "launcher/game/android.rpy", "launcher/game/android.rpyc", "launcher/game/androidstrings.rpy", "launcher/game/androidstrings.rpyc", "launcher/game/archiver.rpy", "launcher/game/archiver.rpyc", "launcher/game/cache/bytecode-312.rpyb", "launcher/game/cache/py3analysis.rpyb", "launcher/game/cache/pyanalysis.rpyb", "launcher/game/cache/screens.rpyb", "launcher/game/cache/shaders.txt", "launcher/game/change_icon.py", "launcher/game/choose_directory.rpy", "launcher/game/choose_directory.rpyc", "launcher/game/choose_theme.rpy", "launcher/game/choose_theme.rpyc", "launcher/game/consolecommand.rpy", "launcher/game/consolecommand.rpyc", "launcher/game/distribute.rpy", "launcher/game/distribute.rpyc", "launcher/game/distribute_gui.rpy", "launcher/game/distribute_gui.rpyc", "launcher/game/dmgcheck.rpy", "launcher/game/dmgcheck.rpyc", "launcher/game/download.rpy", "launcher/game/download.rpyc", "launcher/game/editor.rpy", "launcher/game/editor.rpyc", "launcher/game/entitlements.plist", "launcher/game/fonts/Roboto-Light.ttf", "launcher/game/fonts/Roboto-Regular.ttf", "launcher/game/front_page.rpy", "launcher/game/front_page.rpyc", "launcher/game/gui7.rpy", "launcher/game/gui7.rpyc", "launcher/game/gui7/__init__.py", "launcher/game/gui7/code.py", "launcher/game/gui7/icon.png", "launcher/game/gui7/images.py", "launcher/game/gui7/parameters.py", "launcher/game/images/background.png", "launcher/game/images/folder.svg", "launcher/game/images/logo.png", "launcher/game/images/logo32.png", "launcher/game/images/pattern.png", "launcher/game/images/window-icon-mac.png", "launcher/game/images/window-icon.png", "launcher/game/images/window.png", "launcher/game/install.rpy", "launcher/game/install.rpyc", "launcher/game/installer.py", "launcher/game/installer.rpy", "launcher/game/installer.rpyc", "launcher/game/interface.rpy", "launcher/game/interface.rpyc", "launcher/game/ios.rpy", "launcher/game/ios.rpyc", "launcher/game/itch.rpy", "launcher/game/itch.rpyc", "launcher/game/mac.rpy", "launcher/game/mac.rpyc", "launcher/game/mobilebuild.rpy", "launcher/game/mobilebuild.rpyc", "launcher/game/navigation.rpy", "launcher/game/navigation.rpyc", "launcher/game/new_project.rpy", "launcher/game/new_project.rpyc", "launcher/game/options.rpy", "launcher/game/options.rpyc", "launcher/game/package_formats.rpy", "launcher/game/package_formats.rpyc", "launcher/game/preferences.rpy", "launcher/game/preferences.rpyc", "launcher/game/project.rpy", "launcher/game/project.rpyc", "launcher/game/rapt_hash.txt", "launcher/game/renios_hash.txt", "launcher/game/renpy_ecdsa_public.pem", "launcher/game/renpy_public.pem", "launcher/game/style.rpy", "launcher/game/style.rpyc", "launcher/game/tail.rpy", "launcher/game/tail.rpyc", "launcher/game/testcases.rpy", "launcher/game/testcases.rpyc", "launcher/game/theme_data.rpy", "launcher/game/theme_data.rpyc", "launcher/game/tl/arabic/common.rpy", "launcher/game/tl/arabic/common.rpyc", "launcher/game/tl/arabic/developer.rpy", "launcher/game/tl/arabic/developer.rpyc", "launcher/game/tl/arabic/error.rpy", "launcher/game/tl/arabic/error.rpyc", "launcher/game/tl/arabic/gui.rpy", "launcher/game/tl/arabic/gui.rpyc", "launcher/game/tl/arabic/launcher.rpy", "launcher/game/tl/arabic/launcher.rpyc", "launcher/game/tl/arabic/obsolete.rpy", "launcher/game/tl/arabic/obsolete.rpyc", "launcher/game/tl/arabic/options.rpy", "launcher/game/tl/arabic/options.rpyc", "launcher/game/tl/arabic/screens.rpy", "launcher/game/tl/arabic/screens.rpyc", "launcher/game/tl/arabic/script.rpym", "launcher/game/tl/arabic/style.rpy", "launcher/game/tl/arabic/style.rpyc", "launcher/game/tl/danish/common.rpy", "launcher/game/tl/danish/common.rpyc", "launcher/game/tl/danish/developer.rpy", "launcher/game/tl/danish/developer.rpyc", "launcher/game/tl/danish/error.rpy", "launcher/game/tl/danish/error.rpyc", "launcher/game/tl/danish/gui.rpy", "launcher/game/tl/danish/gui.rpyc", "launcher/game/tl/danish/launcher.rpy", "launcher/game/tl/danish/launcher.rpyc", "launcher/game/tl/danish/obsolete.rpy", "launcher/game/tl/danish/obsolete.rpyc", "launcher/game/tl/danish/options.rpy", "launcher/game/tl/danish/options.rpyc", "launcher/game/tl/danish/screens.rpy", "launcher/game/tl/danish/screens.rpyc", "launcher/game/tl/danish/script.rpym", "launcher/game/tl/finnish/common.rpy", "launcher/game/tl/finnish/common.rpyc", "launcher/game/tl/finnish/developer.rpy", "launcher/game/tl/finnish/developer.rpyc", "launcher/game/tl/finnish/error.rpy", "launcher/game/tl/finnish/error.rpyc", "launcher/game/tl/finnish/gui.rpy", "launcher/game/tl/finnish/gui.rpyc", "launcher/game/tl/finnish/launcher.rpy", "launcher/game/tl/finnish/launcher.rpyc", "launcher/game/tl/finnish/obsolete.rpy", "launcher/game/tl/finnish/obsolete.rpyc", "launcher/game/tl/finnish/options.rpy", "launcher/game/tl/finnish/options.rpyc", "launcher/game/tl/finnish/screens.rpy", "launcher/game/tl/finnish/screens.rpyc", "launcher/game/tl/finnish/style.rpy", "launcher/game/tl/finnish/style.rpyc", "launcher/game/tl/french/common.rpy", "launcher/game/tl/french/common.rpyc", "launcher/game/tl/french/developer.rpy", "launcher/game/tl/french/developer.rpyc", "launcher/game/tl/french/error.rpy", "launcher/game/tl/french/error.rpyc", "launcher/game/tl/french/gui.rpy", "launcher/game/tl/french/gui.rpyc", "launcher/game/tl/french/launcher.rpy", "launcher/game/tl/french/launcher.rpyc", "launcher/game/tl/french/obsolete.rpy", "launcher/game/tl/french/obsolete.rpyc", "launcher/game/tl/french/options.rpy", "launcher/game/tl/french/options.rpyc", "launcher/game/tl/french/screens.rpy", "launcher/game/tl/french/screens.rpyc", "launcher/game/tl/french/script.rpym", "launcher/game/tl/german/common.rpy", "launcher/game/tl/german/common.rpyc", "launcher/game/tl/german/developer.rpy", "launcher/game/tl/german/developer.rpyc", "launcher/game/tl/german/error.rpy", "launcher/game/tl/german/error.rpyc", "launcher/game/tl/german/gui.rpy", "launcher/game/tl/german/gui.rpyc", "launcher/game/tl/german/launcher.rpy", "launcher/game/tl/german/launcher.rpyc", "launcher/game/tl/german/obsolete.rpy", "launcher/game/tl/german/obsolete.rpyc", "launcher/game/tl/german/options.rpy", "launcher/game/tl/german/options.rpyc", "launcher/game/tl/german/screens.rpy", "launcher/game/tl/german/screens.rpyc", "launcher/game/tl/german/script.rpym", "launcher/game/tl/greek/Z_ReadMe.txt", "launcher/game/tl/greek/Z_changelog.txt", "launcher/game/tl/greek/common.rpy", "launcher/game/tl/greek/common.rpyc", "launcher/game/tl/greek/developer.rpy", "launcher/game/tl/greek/developer.rpyc", "launcher/game/tl/greek/error.rpy", "launcher/game/tl/greek/error.rpyc", "launcher/game/tl/greek/gui.rpy", "launcher/game/tl/greek/gui.rpyc", "launcher/game/tl/greek/launcher.rpy", "launcher/game/tl/greek/launcher.rpyc", "launcher/game/tl/greek/obsolete.rpy", "launcher/game/tl/greek/obsolete.rpyc", "launcher/game/tl/greek/options.rpy", "launcher/game/tl/greek/options.rpyc", "launcher/game/tl/greek/screens.rpy", "launcher/game/tl/greek/screens.rpyc", "launcher/game/tl/greek/style.rpy", "launcher/game/tl/greek/style.rpyc", "launcher/game/tl/indonesian/common.rpy", "launcher/game/tl/indonesian/common.rpyc", "launcher/game/tl/indonesian/developer.rpy", "launcher/game/tl/indonesian/developer.rpyc", "launcher/game/tl/indonesian/error.rpy", "launcher/game/tl/indonesian/error.rpyc", "launcher/game/tl/indonesian/gui.rpy", "launcher/game/tl/indonesian/gui.rpyc", "launcher/game/tl/indonesian/launcher.rpy", "launcher/game/tl/indonesian/launcher.rpyc", "launcher/game/tl/indonesian/obsolete.rpy", "launcher/game/tl/indonesian/obsolete.rpyc", "launcher/game/tl/indonesian/options.rpy", "launcher/game/tl/indonesian/options.rpyc", "launcher/game/tl/indonesian/screens.rpy", "launcher/game/tl/indonesian/screens.rpyc", "launcher/game/tl/indonesian/script.rpym", "launcher/game/tl/indonesian/style.rpy", "launcher/game/tl/indonesian/style.rpyc", "launcher/game/tl/italian/common.rpy", "launcher/game/tl/italian/common.rpyc", "launcher/game/tl/italian/developer.rpy", "launcher/game/tl/italian/developer.rpyc", "launcher/game/tl/italian/error.rpy", "launcher/game/tl/italian/error.rpyc", "launcher/game/tl/italian/gui.rpy", "launcher/game/tl/italian/gui.rpyc", "launcher/game/tl/italian/launcher.rpy", "launcher/game/tl/italian/launcher.rpyc", "launcher/game/tl/italian/obsolete.rpy", "launcher/game/tl/italian/obsolete.rpyc", "launcher/game/tl/italian/options.rpy", "launcher/game/tl/italian/options.rpyc", "launcher/game/tl/italian/screens.rpy", "launcher/game/tl/italian/screens.rpyc", "launcher/game/tl/italian/script.rpym", "launcher/game/tl/japanese/common.rpy", "launcher/game/tl/japanese/common.rpyc", "launcher/game/tl/japanese/developer.rpy", "launcher/game/tl/japanese/developer.rpyc", "launcher/game/tl/japanese/error.rpy", "launcher/game/tl/japanese/error.rpyc", "launcher/game/tl/japanese/gui.rpy", "launcher/game/tl/japanese/gui.rpyc", "launcher/game/tl/japanese/launcher.rpy", "launcher/game/tl/japanese/launcher.rpyc", "launcher/game/tl/japanese/obsolete.rpy", "launcher/game/tl/japanese/obsolete.rpyc", "launcher/game/tl/japanese/options.rpy", "launcher/game/tl/japanese/options.rpyc", "launcher/game/tl/japanese/screens.rpy", "launcher/game/tl/japanese/screens.rpyc", "launcher/game/tl/japanese/script.rpym", "launcher/game/tl/japanese/style.rpy", "launcher/game/tl/japanese/style.rpyc", "launcher/game/tl/korean/common.rpy", "launcher/game/tl/korean/common.rpyc", "launcher/game/tl/korean/developer.rpy", "launcher/game/tl/korean/developer.rpyc", "launcher/game/tl/korean/error.rpy", "launcher/game/tl/korean/error.rpyc", "launcher/game/tl/korean/gui.rpy", "launcher/game/tl/korean/gui.rpyc", "launcher/game/tl/korean/launcher.rpy", "launcher/game/tl/korean/launcher.rpyc", "launcher/game/tl/korean/obsolete.rpy", "launcher/game/tl/korean/obsolete.rpyc", "launcher/game/tl/korean/options.rpy", "launcher/game/tl/korean/options.rpyc", "launcher/game/tl/korean/screens.rpy", "launcher/game/tl/korean/screens.rpyc", "launcher/game/tl/korean/script.rpym", "launcher/game/tl/korean/style.rpy", "launcher/game/tl/korean/style.rpyc", "launcher/game/tl/malay/common.rpy", "launcher/game/tl/malay/common.rpyc", "launcher/game/tl/malay/developer.rpy", "launcher/game/tl/malay/developer.rpyc", "launcher/game/tl/malay/error.rpy", "launcher/game/tl/malay/error.rpyc", "launcher/game/tl/malay/gui.rpy", "launcher/game/tl/malay/gui.rpyc", "launcher/game/tl/malay/launcher.rpy", "launcher/game/tl/malay/launcher.rpyc", "launcher/game/tl/malay/options.rpy", "launcher/game/tl/malay/options.rpyc", "launcher/game/tl/malay/screens.rpy", "launcher/game/tl/malay/screens.rpyc", "launcher/game/tl/malay/script.rpym", "launcher/game/tl/malay/style.rpy", "launcher/game/tl/malay/style.rpyc", "launcher/game/tl/piglatin/common.rpy", "launcher/game/tl/piglatin/common.rpyc", "launcher/game/tl/piglatin/developer.rpy", "launcher/game/tl/piglatin/developer.rpyc", "launcher/game/tl/piglatin/error.rpy", "launcher/game/tl/piglatin/error.rpyc", "launcher/game/tl/piglatin/gui.rpy", "launcher/game/tl/piglatin/gui.rpyc", "launcher/game/tl/piglatin/launcher.rpy", "launcher/game/tl/piglatin/launcher.rpyc", "launcher/game/tl/piglatin/obsolete.rpy", "launcher/game/tl/piglatin/obsolete.rpyc", "launcher/game/tl/piglatin/options.rpy", "launcher/game/tl/piglatin/options.rpyc", "launcher/game/tl/piglatin/screens.rpy", "launcher/game/tl/piglatin/screens.rpyc", "launcher/game/tl/piglatin/script.rpym", "launcher/game/tl/polish/common.rpy", "launcher/game/tl/polish/common.rpyc", "launcher/game/tl/polish/developer.rpy", "launcher/game/tl/polish/developer.rpyc", "launcher/game/tl/polish/error.rpy", "launcher/game/tl/polish/error.rpyc", "launcher/game/tl/polish/gui.rpy", "launcher/game/tl/polish/gui.rpyc", "launcher/game/tl/polish/launcher.rpy", "launcher/game/tl/polish/launcher.rpyc", "launcher/game/tl/polish/options.rpy", "launcher/game/tl/polish/options.rpyc", "launcher/game/tl/polish/screens.rpy", "launcher/game/tl/polish/screens.rpyc", "launcher/game/tl/polish/script.rpym", "launcher/game/tl/portuguese/about.rpy", "launcher/game/tl/portuguese/about.rpyc", "launcher/game/tl/portuguese/add_file.rpy", "launcher/game/tl/portuguese/add_file.rpyc", "launcher/game/tl/portuguese/android.rpy", "launcher/game/tl/portuguese/android.rpyc", "launcher/game/tl/portuguese/choose_directory.rpy", "launcher/game/tl/portuguese/choose_directory.rpyc", "launcher/game/tl/portuguese/choose_theme.rpy", "launcher/game/tl/portuguese/choose_theme.rpyc", "launcher/game/tl/portuguese/common.rpy", "launcher/game/tl/portuguese/common.rpyc", "launcher/game/tl/portuguese/developer.rpy", "launcher/game/tl/portuguese/developer.rpyc", "launcher/game/tl/portuguese/distribute.rpy", "launcher/game/tl/portuguese/distribute.rpyc", "launcher/game/tl/portuguese/distribute_gui.rpy", "launcher/game/tl/portuguese/distribute_gui.rpyc", "launcher/game/tl/portuguese/editor.rpy", "launcher/game/tl/portuguese/editor.rpyc", "launcher/game/tl/portuguese/error.rpy", "launcher/game/tl/portuguese/error.rpyc", "launcher/game/tl/portuguese/front_page.rpy", "launcher/game/tl/portuguese/front_page.rpyc", "launcher/game/tl/portuguese/gui.rpy", "launcher/game/tl/portuguese/gui.rpyc", "launcher/game/tl/portuguese/interface.rpy", "launcher/game/tl/portuguese/interface.rpyc", "launcher/game/tl/portuguese/ios.rpy", "launcher/game/tl/portuguese/ios.rpyc", "launcher/game/tl/portuguese/launcher.rpy", "launcher/game/tl/portuguese/launcher.rpyc", "launcher/game/tl/portuguese/navigation.rpy", "launcher/game/tl/portuguese/navigation.rpyc", "launcher/game/tl/portuguese/new_project.rpy", "launcher/game/tl/portuguese/new_project.rpyc", "launcher/game/tl/portuguese/options.rpy", "launcher/game/tl/portuguese/options.rpyc", "launcher/game/tl/portuguese/preferences.rpy", "launcher/game/tl/portuguese/preferences.rpyc", "launcher/game/tl/portuguese/project.rpy", "launcher/game/tl/portuguese/project.rpyc", "launcher/game/tl/portuguese/screens.rpy", "launcher/game/tl/portuguese/screens.rpyc", "launcher/game/tl/portuguese/style.rpy", "launcher/game/tl/portuguese/style.rpyc", "launcher/game/tl/portuguese/translations.rpy", "launcher/game/tl/portuguese/translations.rpyc", "launcher/game/tl/portuguese/updater.rpy", "launcher/game/tl/portuguese/updater.rpyc", "launcher/game/tl/russian/common.rpy", "launcher/game/tl/russian/common.rpyc", "launcher/game/tl/russian/developer.rpy", "launcher/game/tl/russian/developer.rpyc", "launcher/game/tl/russian/error.rpy", "launcher/game/tl/russian/error.rpyc", "launcher/game/tl/russian/gui.rpy", "launcher/game/tl/russian/gui.rpyc", "launcher/game/tl/russian/launcher.rpy", "launcher/game/tl/russian/launcher.rpyc", "launcher/game/tl/russian/obsolete.rpy", "launcher/game/tl/russian/obsolete.rpyc", "launcher/game/tl/russian/options.rpy", "launcher/game/tl/russian/options.rpyc", "launcher/game/tl/russian/screens.rpy", "launcher/game/tl/russian/screens.rpyc", "launcher/game/tl/russian/script.rpym", "launcher/game/tl/schinese/common.rpy", "launcher/game/tl/schinese/common.rpyc", "launcher/game/tl/schinese/developer.rpy", "launcher/game/tl/schinese/developer.rpyc", "launcher/game/tl/schinese/error.rpy", "launcher/game/tl/schinese/error.rpyc", "launcher/game/tl/schinese/gui.rpy", "launcher/game/tl/schinese/gui.rpyc", "launcher/game/tl/schinese/launcher.rpy", "launcher/game/tl/schinese/launcher.rpyc", "launcher/game/tl/schinese/options.rpy", "launcher/game/tl/schinese/options.rpyc", "launcher/game/tl/schinese/screens.rpy", "launcher/game/tl/schinese/screens.rpyc", "launcher/game/tl/schinese/script.rpym", "launcher/game/tl/schinese/style.rpy", "launcher/game/tl/schinese/style.rpyc", "launcher/game/tl/spanish/common.rpy", "launcher/game/tl/spanish/common.rpyc", "launcher/game/tl/spanish/developer.rpy", "launcher/game/tl/spanish/developer.rpyc", "launcher/game/tl/spanish/error.rpy", "launcher/game/tl/spanish/error.rpyc", "launcher/game/tl/spanish/gui.rpy", "launcher/game/tl/spanish/gui.rpyc", "launcher/game/tl/spanish/launcher.rpy", "launcher/game/tl/spanish/launcher.rpyc", "launcher/game/tl/spanish/obsolete.rpy", "launcher/game/tl/spanish/obsolete.rpyc", "launcher/game/tl/spanish/options.rpy", "launcher/game/tl/spanish/options.rpyc", "launcher/game/tl/spanish/screens.rpy", "launcher/game/tl/spanish/screens.rpyc", "launcher/game/tl/spanish/script.rpym", "launcher/game/tl/tchinese/common.rpy", "launcher/game/tl/tchinese/common.rpyc", "launcher/game/tl/tchinese/developer.rpy", "launcher/game/tl/tchinese/developer.rpyc", "launcher/game/tl/tchinese/error.rpy", "launcher/game/tl/tchinese/error.rpyc", "launcher/game/tl/tchinese/gui.rpy", "launcher/game/tl/tchinese/gui.rpyc", "launcher/game/tl/tchinese/launcher.rpy", "launcher/game/tl/tchinese/launcher.rpyc", "launcher/game/tl/tchinese/options.rpy", "launcher/game/tl/tchinese/options.rpyc", "launcher/game/tl/tchinese/screens.rpy", "launcher/game/tl/tchinese/screens.rpyc", "launcher/game/tl/tchinese/script.rpym", "launcher/game/tl/tchinese/style.rpy", "launcher/game/tl/tchinese/style.rpyc", "launcher/game/tl/turkish/common.rpy", "launcher/game/tl/turkish/common.rpyc", "launcher/game/tl/turkish/developer.rpy", "launcher/game/tl/turkish/developer.rpyc", "launcher/game/tl/turkish/error.rpy", "launcher/game/tl/turkish/error.rpyc", "launcher/game/tl/turkish/gui.rpy", "launcher/game/tl/turkish/gui.rpyc", "launcher/game/tl/turkish/launcher.rpy", "launcher/game/tl/turkish/launcher.rpyc", "launcher/game/tl/turkish/options.rpy", "launcher/game/tl/turkish/options.rpyc", "launcher/game/tl/turkish/screens.rpy", "launcher/game/tl/turkish/screens.rpyc", "launcher/game/tl/turkish/script.rpym", "launcher/game/tl/ukrainian/common.rpy", "launcher/game/tl/ukrainian/common.rpyc", "launcher/game/tl/ukrainian/developer.rpy", "launcher/game/tl/ukrainian/developer.rpyc", "launcher/game/tl/ukrainian/error.rpy", "launcher/game/tl/ukrainian/error.rpyc", "launcher/game/tl/ukrainian/gui.rpy", "launcher/game/tl/ukrainian/gui.rpyc", "launcher/game/tl/ukrainian/launcher.rpy", "launcher/game/tl/ukrainian/launcher.rpyc", "launcher/game/tl/ukrainian/options.rpy", "launcher/game/tl/ukrainian/options.rpyc", "launcher/game/tl/ukrainian/screens.rpy", "launcher/game/tl/ukrainian/screens.rpyc", "launcher/game/tl/ukrainian/script.rpym", "launcher/game/tl/vietnamese/common.rpy", "launcher/game/tl/vietnamese/common.rpyc", "launcher/game/tl/vietnamese/developer.rpy", "launcher/game/tl/vietnamese/developer.rpyc", "launcher/game/tl/vietnamese/error.rpy", "launcher/game/tl/vietnamese/error.rpyc", "launcher/game/tl/vietnamese/gui.rpy", "launcher/game/tl/vietnamese/gui.rpyc", "launcher/game/tl/vietnamese/launcher.rpy", "launcher/game/tl/vietnamese/launcher.rpyc", "launcher/game/tl/vietnamese/obsolete.rpy", "launcher/game/tl/vietnamese/obsolete.rpyc", "launcher/game/tl/vietnamese/options.rpy", "launcher/game/tl/vietnamese/options.rpyc", "launcher/game/tl/vietnamese/screens.rpy", "launcher/game/tl/vietnamese/screens.rpyc", "launcher/game/translations.rpy", "launcher/game/translations.rpyc", "launcher/game/updater.rpy", "launcher/game/updater.rpyc", "launcher/game/util.rpy", "launcher/game/util.rpyc", "launcher/game/web.rpy", "launcher/game/web.rpyc", "launcher/game/web_hash.txt", "launcher/game/webserver.py", "launcher/icon.icns", "launcher/project.json", "launcher/skin/skin.rpy", "launcher/skin/skin.rpyc", "launcher/skin/skin_background.jpg", "lib/py3-linux-x86_64/librenpython.so", "lib/py3-linux-x86_64/python", "lib/py3-linux-x86_64/pythonw", "lib/py3-linux-x86_64/renpy", "lib/py3-linux-x86_64/zsync", "lib/py3-linux-x86_64/zsyncmake", "lib/py3-mac-universal/librenpython.dylib", "lib/py3-mac-universal/python", "lib/py3-mac-universal/pythonw", "lib/py3-mac-universal/renpy", "lib/py3-mac-universal/zsync", "lib/py3-mac-universal/zsyncmake", "lib/py3-windows-x86_64/d3dcompiler_47.dll", "lib/py3-windows-x86_64/libEGL.dll", "lib/py3-windows-x86_64/libGLESv2.dll", "lib/py3-windows-x86_64/libpython3.12.dll", "lib/py3-windows-x86_64/librenpython.dll", "lib/py3-windows-x86_64/libwinpthread-1.dll", "lib/py3-windows-x86_64/libwinpthread-1.txt", "lib/py3-windows-x86_64/nvdrs.dll", "lib/py3-windows-x86_64/python.exe", "lib/py3-windows-x86_64/pythonw.exe", "lib/py3-windows-x86_64/renpy.exe", "lib/py3-windows-x86_64/say.vbs", "lib/py3-windows-x86_64/zsync.exe", "lib/py3-windows-x86_64/zsyncmake.exe", "lib/python3.12/__future__.pyc", "lib/python3.12/_collections_abc.pyc", "lib/python3.12/_compat_pickle.pyc", "lib/python3.12/_compression.pyc", "lib/python3.12/_markupbase.pyc", "lib/python3.12/_osx_support.pyc", "lib/python3.12/_py_abc.pyc", "lib/python3.12/_pydecimal.pyc", "lib/python3.12/_pyio.pyc", "lib/python3.12/_sitebuiltins.pyc", "lib/python3.12/_strptime.pyc", "lib/python3.12/_threading_local.pyc", "lib/python3.12/_weakrefset.pyc", "lib/python3.12/abc.pyc", "lib/python3.12/android/__init__.pyc", "lib/python3.12/android/apk.pyc", "lib/python3.12/argparse.pyc", "lib/python3.12/ast.pyc", "lib/python3.12/asyncio/__init__.pyc", "lib/python3.12/asyncio/__main__.pyc", "lib/python3.12/asyncio/base_events.pyc", "lib/python3.12/asyncio/base_futures.pyc", "lib/python3.12/asyncio/base_subprocess.pyc", "lib/python3.12/asyncio/base_tasks.pyc", "lib/python3.12/asyncio/constants.pyc", "lib/python3.12/asyncio/coroutines.pyc", "lib/python3.12/asyncio/events.pyc", "lib/python3.12/asyncio/exceptions.pyc", "lib/python3.12/asyncio/format_helpers.pyc", "lib/python3.12/asyncio/futures.pyc", "lib/python3.12/asyncio/locks.pyc", "lib/python3.12/asyncio/log.pyc", "lib/python3.12/asyncio/mixins.pyc", "lib/python3.12/asyncio/proactor_events.pyc", "lib/python3.12/asyncio/protocols.pyc", "lib/python3.12/asyncio/queues.pyc", "lib/python3.12/asyncio/runners.pyc", "lib/python3.12/asyncio/selector_events.pyc", "lib/python3.12/asyncio/sslproto.pyc", "lib/python3.12/asyncio/staggered.pyc", "lib/python3.12/asyncio/streams.pyc", "lib/python3.12/asyncio/subprocess.pyc", "lib/python3.12/asyncio/taskgroups.pyc", "lib/python3.12/asyncio/tasks.pyc", "lib/python3.12/asyncio/threads.pyc", "lib/python3.12/asyncio/timeouts.pyc", "lib/python3.12/asyncio/transports.pyc", "lib/python3.12/asyncio/trsock.pyc", "lib/python3.12/asyncio/unix_events.pyc", "lib/python3.12/asyncio/windows_events.pyc", "lib/python3.12/asyncio/windows_utils.pyc", "lib/python3.12/base64.pyc", "lib/python3.12/bisect.pyc", "lib/python3.12/bz2.pyc", "lib/python3.12/cProfile.pyc", "lib/python3.12/calendar.pyc", "lib/python3.12/certifi/__init__.pyc", "lib/python3.12/certifi/__main__.pyc", "lib/python3.12/certifi/cacert.pem", "lib/python3.12/certifi/core.pyc", "lib/python3.12/cgi.pyc", "lib/python3.12/chardet/__init__.pyc", "lib/python3.12/chardet/__main__.pyc", "lib/python3.12/chardet/big5freq.pyc", "lib/python3.12/chardet/big5prober.pyc", "lib/python3.12/chardet/chardistribution.pyc", "lib/python3.12/chardet/charsetgroupprober.pyc", "lib/python3.12/chardet/charsetprober.pyc", "lib/python3.12/chardet/cli/__init__.pyc", "lib/python3.12/chardet/cli/chardetect.pyc", "lib/python3.12/chardet/codingstatemachine.pyc", "lib/python3.12/chardet/codingstatemachinedict.pyc", "lib/python3.12/chardet/cp949prober.pyc", "lib/python3.12/chardet/enums.pyc", "lib/python3.12/chardet/escprober.pyc", "lib/python3.12/chardet/escsm.pyc", "lib/python3.12/chardet/eucjpprober.pyc", "lib/python3.12/chardet/euckrfreq.pyc", "lib/python3.12/chardet/euckrprober.pyc", "lib/python3.12/chardet/euctwfreq.pyc", "lib/python3.12/chardet/euctwprober.pyc", "lib/python3.12/chardet/gb2312freq.pyc", "lib/python3.12/chardet/gb2312prober.pyc", "lib/python3.12/chardet/hebrewprober.pyc", "lib/python3.12/chardet/jisfreq.pyc", "lib/python3.12/chardet/johabfreq.pyc", "lib/python3.12/chardet/johabprober.pyc", "lib/python3.12/chardet/jpcntx.pyc", "lib/python3.12/chardet/langbulgarianmodel.pyc", "lib/python3.12/chardet/langgreekmodel.pyc", "lib/python3.12/chardet/langhebrewmodel.pyc", "lib/python3.12/chardet/langhungarianmodel.pyc", "lib/python3.12/chardet/langrussianmodel.pyc", "lib/python3.12/chardet/langthaimodel.pyc", "lib/python3.12/chardet/langturkishmodel.pyc", "lib/python3.12/chardet/latin1prober.pyc", "lib/python3.12/chardet/macromanprober.pyc", "lib/python3.12/chardet/mbcharsetprober.pyc", "lib/python3.12/chardet/mbcsgroupprober.pyc", "lib/python3.12/chardet/mbcssm.pyc", "lib/python3.12/chardet/metadata/__init__.pyc", "lib/python3.12/chardet/metadata/languages.pyc", "lib/python3.12/chardet/resultdict.pyc", "lib/python3.12/chardet/sbcharsetprober.pyc", "lib/python3.12/chardet/sbcsgroupprober.pyc", "lib/python3.12/chardet/sjisprober.pyc", "lib/python3.12/chardet/universaldetector.pyc", "lib/python3.12/chardet/utf1632prober.pyc", "lib/python3.12/chardet/utf8prober.pyc", "lib/python3.12/chardet/version.pyc", "lib/python3.12/chunk.pyc", "lib/python3.12/cmd.pyc", "lib/python3.12/code.pyc", "lib/python3.12/codecs.pyc", "lib/python3.12/codeop.pyc", "lib/python3.12/collections/__init__.pyc", "lib/python3.12/collections/abc.pyc", "lib/python3.12/colorsys.pyc", "lib/python3.12/compileall.pyc", "lib/python3.12/concurrent/__init__.pyc", "lib/python3.12/concurrent/futures/__init__.pyc", "lib/python3.12/concurrent/futures/_base.pyc", "lib/python3.12/concurrent/futures/process.pyc", "lib/python3.12/concurrent/futures/thread.pyc", "lib/python3.12/configparser.pyc", "lib/python3.12/contextlib.pyc", "lib/python3.12/contextvars.pyc", "lib/python3.12/copy.pyc", "lib/python3.12/copyreg.pyc", "lib/python3.12/csv.pyc", "lib/python3.12/ctypes/__init__.pyc", "lib/python3.12/ctypes/_aix.pyc", "lib/python3.12/ctypes/_endian.pyc", "lib/python3.12/ctypes/macholib/__init__.pyc", "lib/python3.12/ctypes/macholib/dyld.pyc", "lib/python3.12/ctypes/macholib/dylib.pyc", "lib/python3.12/ctypes/macholib/framework.pyc", "lib/python3.12/ctypes/util.pyc", "lib/python3.12/ctypes/wintypes.pyc", "lib/python3.12/dataclasses.pyc", "lib/python3.12/datetime.pyc", "lib/python3.12/decimal.pyc", "lib/python3.12/difflib.pyc", "lib/python3.12/dis.pyc", "lib/python3.12/doctest.pyc", "lib/python3.12/ecdsa/__init__.pyc", "lib/python3.12/ecdsa/_compat.pyc", "lib/python3.12/ecdsa/_rwlock.pyc", "lib/python3.12/ecdsa/_sha3.pyc", "lib/python3.12/ecdsa/_version.pyc", "lib/python3.12/ecdsa/curves.pyc", "lib/python3.12/ecdsa/der.pyc", "lib/python3.12/ecdsa/ecdh.pyc", "lib/python3.12/ecdsa/ecdsa.pyc", "lib/python3.12/ecdsa/eddsa.pyc", "lib/python3.12/ecdsa/ellipticcurve.pyc", "lib/python3.12/ecdsa/errors.pyc", "lib/python3.12/ecdsa/keys.pyc", "lib/python3.12/ecdsa/numbertheory.pyc", "lib/python3.12/ecdsa/rfc6979.pyc", "lib/python3.12/ecdsa/ssh.pyc", "lib/python3.12/ecdsa/test_curves.pyc", "lib/python3.12/ecdsa/test_der.pyc", "lib/python3.12/ecdsa/test_ecdh.pyc", "lib/python3.12/ecdsa/test_ecdsa.pyc", "lib/python3.12/ecdsa/test_eddsa.pyc", "lib/python3.12/ecdsa/test_ellipticcurve.pyc", "lib/python3.12/ecdsa/test_jacobi.pyc", "lib/python3.12/ecdsa/test_keys.pyc", "lib/python3.12/ecdsa/test_malformed_sigs.pyc", "lib/python3.12/ecdsa/test_numbertheory.pyc", "lib/python3.12/ecdsa/test_pyecdsa.pyc", "lib/python3.12/ecdsa/test_rw_lock.pyc", "lib/python3.12/ecdsa/test_sha3.pyc", "lib/python3.12/ecdsa/util.pyc", "lib/python3.12/email/__init__.pyc", "lib/python3.12/email/_encoded_words.pyc", "lib/python3.12/email/_header_value_parser.pyc", "lib/python3.12/email/_parseaddr.pyc", "lib/python3.12/email/_policybase.pyc", "lib/python3.12/email/base64mime.pyc", "lib/python3.12/email/charset.pyc", "lib/python3.12/email/contentmanager.pyc", "lib/python3.12/email/encoders.pyc", "lib/python3.12/email/errors.pyc", "lib/python3.12/email/feedparser.pyc", "lib/python3.12/email/generator.pyc", "lib/python3.12/email/header.pyc", "lib/python3.12/email/headerregistry.pyc", "lib/python3.12/email/iterators.pyc", "lib/python3.12/email/message.pyc", "lib/python3.12/email/mime/__init__.pyc", "lib/python3.12/email/mime/application.pyc", "lib/python3.12/email/mime/audio.pyc", "lib/python3.12/email/mime/base.pyc", "lib/python3.12/email/mime/image.pyc", "lib/python3.12/email/mime/message.pyc", "lib/python3.12/email/mime/multipart.pyc", "lib/python3.12/email/mime/nonmultipart.pyc", "lib/python3.12/email/mime/text.pyc", "lib/python3.12/email/parser.pyc", "lib/python3.12/email/policy.pyc", "lib/python3.12/email/quoprimime.pyc", "lib/python3.12/email/utils.pyc", "lib/python3.12/encodings/__init__.pyc", "lib/python3.12/encodings/aliases.pyc", "lib/python3.12/encodings/ascii.pyc", "lib/python3.12/encodings/base64_codec.pyc", "lib/python3.12/encodings/big5.pyc", "lib/python3.12/encodings/big5hkscs.pyc", "lib/python3.12/encodings/bz2_codec.pyc", "lib/python3.12/encodings/charmap.pyc", "lib/python3.12/encodings/cp037.pyc", "lib/python3.12/encodings/cp1006.pyc", "lib/python3.12/encodings/cp1026.pyc", "lib/python3.12/encodings/cp1125.pyc", "lib/python3.12/encodings/cp1140.pyc", "lib/python3.12/encodings/cp1250.pyc", "lib/python3.12/encodings/cp1251.pyc", "lib/python3.12/encodings/cp1252.pyc", "lib/python3.12/encodings/cp1253.pyc", "lib/python3.12/encodings/cp1254.pyc", "lib/python3.12/encodings/cp1255.pyc", "lib/python3.12/encodings/cp1256.pyc", "lib/python3.12/encodings/cp1257.pyc", "lib/python3.12/encodings/cp1258.pyc", "lib/python3.12/encodings/cp273.pyc", "lib/python3.12/encodings/cp424.pyc", "lib/python3.12/encodings/cp437.pyc", "lib/python3.12/encodings/cp500.pyc", "lib/python3.12/encodings/cp720.pyc", "lib/python3.12/encodings/cp737.pyc", "lib/python3.12/encodings/cp775.pyc", "lib/python3.12/encodings/cp850.pyc", "lib/python3.12/encodings/cp852.pyc", "lib/python3.12/encodings/cp855.pyc", "lib/python3.12/encodings/cp856.pyc", "lib/python3.12/encodings/cp857.pyc", "lib/python3.12/encodings/cp858.pyc", "lib/python3.12/encodings/cp860.pyc", "lib/python3.12/encodings/cp861.pyc", "lib/python3.12/encodings/cp862.pyc", "lib/python3.12/encodings/cp863.pyc", "lib/python3.12/encodings/cp864.pyc", "lib/python3.12/encodings/cp865.pyc", "lib/python3.12/encodings/cp866.pyc", "lib/python3.12/encodings/cp869.pyc", "lib/python3.12/encodings/cp874.pyc", "lib/python3.12/encodings/cp875.pyc", "lib/python3.12/encodings/cp932.pyc", "lib/python3.12/encodings/cp949.pyc", "lib/python3.12/encodings/cp950.pyc", "lib/python3.12/encodings/euc_jis_2004.pyc", "lib/python3.12/encodings/euc_jisx0213.pyc", "lib/python3.12/encodings/euc_jp.pyc", "lib/python3.12/encodings/euc_kr.pyc", "lib/python3.12/encodings/gb18030.pyc", "lib/python3.12/encodings/gb2312.pyc", "lib/python3.12/encodings/gbk.pyc", "lib/python3.12/encodings/hex_codec.pyc", "lib/python3.12/encodings/hp_roman8.pyc", "lib/python3.12/encodings/hz.pyc", "lib/python3.12/encodings/idna.pyc", "lib/python3.12/encodings/iso2022_jp.pyc", "lib/python3.12/encodings/iso2022_jp_1.pyc", "lib/python3.12/encodings/iso2022_jp_2.pyc", "lib/python3.12/encodings/iso2022_jp_2004.pyc", "lib/python3.12/encodings/iso2022_jp_3.pyc", "lib/python3.12/encodings/iso2022_jp_ext.pyc", "lib/python3.12/encodings/iso2022_kr.pyc", "lib/python3.12/encodings/iso8859_1.pyc", "lib/python3.12/encodings/iso8859_10.pyc", "lib/python3.12/encodings/iso8859_11.pyc", "lib/python3.12/encodings/iso8859_13.pyc", "lib/python3.12/encodings/iso8859_14.pyc", "lib/python3.12/encodings/iso8859_15.pyc", "lib/python3.12/encodings/iso8859_16.pyc", "lib/python3.12/encodings/iso8859_2.pyc", "lib/python3.12/encodings/iso8859_3.pyc", "lib/python3.12/encodings/iso8859_4.pyc", "lib/python3.12/encodings/iso8859_5.pyc", "lib/python3.12/encodings/iso8859_6.pyc", "lib/python3.12/encodings/iso8859_7.pyc", "lib/python3.12/encodings/iso8859_8.pyc", "lib/python3.12/encodings/iso8859_9.pyc", "lib/python3.12/encodings/johab.pyc", "lib/python3.12/encodings/koi8_r.pyc", "lib/python3.12/encodings/koi8_t.pyc", "lib/python3.12/encodings/koi8_u.pyc", "lib/python3.12/encodings/kz1048.pyc", "lib/python3.12/encodings/latin_1.pyc", "lib/python3.12/encodings/mac_arabic.pyc", "lib/python3.12/encodings/mac_croatian.pyc", "lib/python3.12/encodings/mac_cyrillic.pyc", "lib/python3.12/encodings/mac_farsi.pyc", "lib/python3.12/encodings/mac_greek.pyc", "lib/python3.12/encodings/mac_iceland.pyc", "lib/python3.12/encodings/mac_latin2.pyc", "lib/python3.12/encodings/mac_roman.pyc", "lib/python3.12/encodings/mac_romanian.pyc", "lib/python3.12/encodings/mac_turkish.pyc", "lib/python3.12/encodings/mbcs.pyc", "lib/python3.12/encodings/oem.pyc", "lib/python3.12/encodings/palmos.pyc", "lib/python3.12/encodings/ptcp154.pyc", "lib/python3.12/encodings/punycode.pyc", "lib/python3.12/encodings/quopri_codec.pyc", "lib/python3.12/encodings/raw_unicode_escape.pyc", "lib/python3.12/encodings/rot_13.pyc", "lib/python3.12/encodings/shift_jis.pyc", "lib/python3.12/encodings/shift_jis_2004.pyc", "lib/python3.12/encodings/shift_jisx0213.pyc", "lib/python3.12/encodings/tis_620.pyc", "lib/python3.12/encodings/undefined.pyc", "lib/python3.12/encodings/unicode_escape.pyc", "lib/python3.12/encodings/utf_16.pyc", "lib/python3.12/encodings/utf_16_be.pyc", "lib/python3.12/encodings/utf_16_le.pyc", "lib/python3.12/encodings/utf_32.pyc", "lib/python3.12/encodings/utf_32_be.pyc", "lib/python3.12/encodings/utf_32_le.pyc", "lib/python3.12/encodings/utf_7.pyc", "lib/python3.12/encodings/utf_8.pyc", "lib/python3.12/encodings/utf_8_sig.pyc", "lib/python3.12/encodings/uu_codec.pyc", "lib/python3.12/encodings/zlib_codec.pyc", "lib/python3.12/enum.pyc", "lib/python3.12/filecmp.pyc", "lib/python3.12/fileinput.pyc", "lib/python3.12/fnmatch.pyc", "lib/python3.12/fractions.pyc", "lib/python3.12/ftplib.pyc", "lib/python3.12/functools.pyc", "lib/python3.12/future/__init__.pyc", "lib/python3.12/future/backports/__init__.pyc", "lib/python3.12/future/backports/_markupbase.pyc", "lib/python3.12/future/backports/datetime.pyc", "lib/python3.12/future/backports/email/__init__.pyc", "lib/python3.12/future/backports/email/_encoded_words.pyc", "lib/python3.12/future/backports/email/_header_value_parser.pyc", "lib/python3.12/future/backports/email/_parseaddr.pyc", "lib/python3.12/future/backports/email/_policybase.pyc", "lib/python3.12/future/backports/email/base64mime.pyc", "lib/python3.12/future/backports/email/charset.pyc", "lib/python3.12/future/backports/email/encoders.pyc", "lib/python3.12/future/backports/email/errors.pyc", "lib/python3.12/future/backports/email/feedparser.pyc", "lib/python3.12/future/backports/email/generator.pyc", "lib/python3.12/future/backports/email/header.pyc", "lib/python3.12/future/backports/email/headerregistry.pyc", "lib/python3.12/future/backports/email/iterators.pyc", "lib/python3.12/future/backports/email/message.pyc", "lib/python3.12/future/backports/email/mime/__init__.pyc", "lib/python3.12/future/backports/email/mime/application.pyc", "lib/python3.12/future/backports/email/mime/audio.pyc", "lib/python3.12/future/backports/email/mime/base.pyc", "lib/python3.12/future/backports/email/mime/image.pyc", "lib/python3.12/future/backports/email/mime/message.pyc", "lib/python3.12/future/backports/email/mime/multipart.pyc", "lib/python3.12/future/backports/email/mime/nonmultipart.pyc", "lib/python3.12/future/backports/email/mime/text.pyc", "lib/python3.12/future/backports/email/parser.pyc", "lib/python3.12/future/backports/email/policy.pyc", "lib/python3.12/future/backports/email/quoprimime.pyc", "lib/python3.12/future/backports/email/utils.pyc", "lib/python3.12/future/backports/html/__init__.pyc", "lib/python3.12/future/backports/html/entities.pyc", "lib/python3.12/future/backports/html/parser.pyc", "lib/python3.12/future/backports/http/__init__.pyc", "lib/python3.12/future/backports/http/client.pyc", "lib/python3.12/future/backports/http/cookiejar.pyc", "lib/python3.12/future/backports/http/cookies.pyc", "lib/python3.12/future/backports/http/server.pyc", "lib/python3.12/future/backports/misc.pyc", "lib/python3.12/future/backports/socket.pyc", "lib/python3.12/future/backports/socketserver.pyc", "lib/python3.12/future/backports/test/__init__.pyc", "lib/python3.12/future/backports/test/pystone.pyc", "lib/python3.12/future/backports/test/ssl_servers.pyc", "lib/python3.12/future/backports/test/support.pyc", "lib/python3.12/future/backports/total_ordering.pyc", "lib/python3.12/future/backports/urllib/__init__.pyc", "lib/python3.12/future/backports/urllib/error.pyc", "lib/python3.12/future/backports/urllib/parse.pyc", "lib/python3.12/future/backports/urllib/request.pyc", "lib/python3.12/future/backports/urllib/response.pyc", "lib/python3.12/future/backports/urllib/robotparser.pyc", "lib/python3.12/future/backports/xmlrpc/__init__.pyc", "lib/python3.12/future/backports/xmlrpc/client.pyc", "lib/python3.12/future/backports/xmlrpc/server.pyc", "lib/python3.12/future/builtins/__init__.pyc", "lib/python3.12/future/builtins/disabled.pyc", "lib/python3.12/future/builtins/iterators.pyc", "lib/python3.12/future/builtins/misc.pyc", "lib/python3.12/future/builtins/new_min_max.pyc", "lib/python3.12/future/builtins/newnext.pyc", "lib/python3.12/future/builtins/newround.pyc", "lib/python3.12/future/builtins/newsuper.pyc", "lib/python3.12/future/moves/__init__.pyc", "lib/python3.12/future/moves/_dummy_thread.pyc", "lib/python3.12/future/moves/_markupbase.pyc", "lib/python3.12/future/moves/_thread.pyc", "lib/python3.12/future/moves/builtins.pyc", "lib/python3.12/future/moves/collections.pyc", "lib/python3.12/future/moves/configparser.pyc", "lib/python3.12/future/moves/copyreg.pyc", "lib/python3.12/future/moves/dbm/__init__.pyc", "lib/python3.12/future/moves/dbm/dumb.pyc", "lib/python3.12/future/moves/dbm/gnu.pyc", "lib/python3.12/future/moves/dbm/ndbm.pyc", "lib/python3.12/future/moves/html/__init__.pyc", "lib/python3.12/future/moves/html/entities.pyc", "lib/python3.12/future/moves/html/parser.pyc", "lib/python3.12/future/moves/http/__init__.pyc", "lib/python3.12/future/moves/http/client.pyc", "lib/python3.12/future/moves/http/cookiejar.pyc", "lib/python3.12/future/moves/http/cookies.pyc", "lib/python3.12/future/moves/http/server.pyc", "lib/python3.12/future/moves/itertools.pyc", "lib/python3.12/future/moves/multiprocessing.pyc", "lib/python3.12/future/moves/pickle.pyc", "lib/python3.12/future/moves/queue.pyc", "lib/python3.12/future/moves/reprlib.pyc", "lib/python3.12/future/moves/socketserver.pyc", "lib/python3.12/future/moves/subprocess.pyc", "lib/python3.12/future/moves/sys.pyc", "lib/python3.12/future/moves/test/__init__.pyc", "lib/python3.12/future/moves/test/support.pyc", "lib/python3.12/future/moves/tkinter/__init__.pyc", "lib/python3.12/future/moves/tkinter/colorchooser.pyc", "lib/python3.12/future/moves/tkinter/commondialog.pyc", "lib/python3.12/future/moves/tkinter/constants.pyc", "lib/python3.12/future/moves/tkinter/dialog.pyc", "lib/python3.12/future/moves/tkinter/dnd.pyc", "lib/python3.12/future/moves/tkinter/filedialog.pyc", "lib/python3.12/future/moves/tkinter/font.pyc", "lib/python3.12/future/moves/tkinter/messagebox.pyc", "lib/python3.12/future/moves/tkinter/scrolledtext.pyc", "lib/python3.12/future/moves/tkinter/simpledialog.pyc", "lib/python3.12/future/moves/tkinter/tix.pyc", "lib/python3.12/future/moves/tkinter/ttk.pyc", "lib/python3.12/future/moves/urllib/__init__.pyc", "lib/python3.12/future/moves/urllib/error.pyc", "lib/python3.12/future/moves/urllib/parse.pyc", "lib/python3.12/future/moves/urllib/request.pyc", "lib/python3.12/future/moves/urllib/response.pyc", "lib/python3.12/future/moves/urllib/robotparser.pyc", "lib/python3.12/future/moves/winreg.pyc", "lib/python3.12/future/moves/xmlrpc/__init__.pyc", "lib/python3.12/future/moves/xmlrpc/client.pyc", "lib/python3.12/future/moves/xmlrpc/server.pyc", "lib/python3.12/future/standard_library/__init__.pyc", "lib/python3.12/future/tests/__init__.pyc", "lib/python3.12/future/tests/base.pyc", "lib/python3.12/future/types/__init__.pyc", "lib/python3.12/future/types/newbytes.pyc", "lib/python3.12/future/types/newdict.pyc", "lib/python3.12/future/types/newint.pyc", "lib/python3.12/future/types/newlist.pyc", "lib/python3.12/future/types/newmemoryview.pyc", "lib/python3.12/future/types/newobject.pyc", "lib/python3.12/future/types/newopen.pyc", "lib/python3.12/future/types/newrange.pyc", "lib/python3.12/future/types/newstr.pyc", "lib/python3.12/future/utils/__init__.pyc", "lib/python3.12/future/utils/surrogateescape.pyc", "lib/python3.12/genericpath.pyc", "lib/python3.12/getopt.pyc", "lib/python3.12/getpass.pyc", "lib/python3.12/gettext.pyc", "lib/python3.12/glob.pyc", "lib/python3.12/graphlib.pyc", "lib/python3.12/gzip.pyc", "lib/python3.12/hashlib.pyc", "lib/python3.12/heapq.pyc", "lib/python3.12/hmac.pyc", "lib/python3.12/html/__init__.pyc", "lib/python3.12/html/entities.pyc", "lib/python3.12/html/parser.pyc", "lib/python3.12/http/__init__.pyc", "lib/python3.12/http/client.pyc", "lib/python3.12/http/cookiejar.pyc", "lib/python3.12/http/cookies.pyc", "lib/python3.12/http/server.pyc", "lib/python3.12/idna/__init__.pyc", "lib/python3.12/idna/codec.pyc", "lib/python3.12/idna/compat.pyc", "lib/python3.12/idna/core.pyc", "lib/python3.12/idna/idnadata.pyc", "lib/python3.12/idna/intranges.pyc", "lib/python3.12/idna/package_data.pyc", "lib/python3.12/idna/uts46data.pyc", "lib/python3.12/imaplib.pyc", "lib/python3.12/imghdr.pyc", "lib/python3.12/importlib/__init__.pyc", "lib/python3.12/importlib/_abc.pyc", "lib/python3.12/importlib/_bootstrap.pyc", "lib/python3.12/importlib/_bootstrap_external.pyc", "lib/python3.12/importlib/abc.pyc", "lib/python3.12/importlib/machinery.pyc", "lib/python3.12/importlib/metadata/__init__.pyc", "lib/python3.12/importlib/metadata/_adapters.pyc", "lib/python3.12/importlib/metadata/_collections.pyc", "lib/python3.12/importlib/metadata/_functools.pyc", "lib/python3.12/importlib/metadata/_itertools.pyc", "lib/python3.12/importlib/metadata/_meta.pyc", "lib/python3.12/importlib/metadata/_text.pyc", "lib/python3.12/importlib/readers.pyc", "lib/python3.12/importlib/resources/__init__.pyc", "lib/python3.12/importlib/resources/_adapters.pyc", "lib/python3.12/importlib/resources/_common.pyc", "lib/python3.12/importlib/resources/_itertools.pyc", "lib/python3.12/importlib/resources/_legacy.pyc", "lib/python3.12/importlib/resources/abc.pyc", "lib/python3.12/importlib/resources/readers.pyc", "lib/python3.12/importlib/resources/simple.pyc", "lib/python3.12/importlib/simple.pyc", "lib/python3.12/importlib/util.pyc", "lib/python3.12/inspect.pyc", "lib/python3.12/io.pyc", "lib/python3.12/iossupport.pyc", "lib/python3.12/ipaddress.pyc", "lib/python3.12/jnius/__init__.pyc", "lib/python3.12/jnius/env.pyc", "lib/python3.12/jnius/reflect.pyc", "lib/python3.12/jnius/signatures.pyc", "lib/python3.12/json/__init__.pyc", "lib/python3.12/json/decoder.pyc", "lib/python3.12/json/encoder.pyc", "lib/python3.12/json/scanner.pyc", "lib/python3.12/json/tool.pyc", "lib/python3.12/keyword.pyc", "lib/python3.12/lib-dynload/empty.txt", "lib/python3.12/linecache.pyc", "lib/python3.12/locale.pyc", "lib/python3.12/logging/__init__.pyc", "lib/python3.12/logging/config.pyc", "lib/python3.12/logging/handlers.pyc", "lib/python3.12/lzma.pyc", "lib/python3.12/mimetypes.pyc", "lib/python3.12/modulefinder.pyc", "lib/python3.12/multiprocessing/__init__.pyc", "lib/python3.12/multiprocessing/connection.pyc", "lib/python3.12/multiprocessing/context.pyc", "lib/python3.12/multiprocessing/dummy/__init__.pyc", "lib/python3.12/multiprocessing/dummy/connection.pyc", "lib/python3.12/multiprocessing/forkserver.pyc", "lib/python3.12/multiprocessing/heap.pyc", "lib/python3.12/multiprocessing/managers.pyc", "lib/python3.12/multiprocessing/pool.pyc", "lib/python3.12/multiprocessing/popen_fork.pyc", "lib/python3.12/multiprocessing/popen_forkserver.pyc", "lib/python3.12/multiprocessing/popen_spawn_posix.pyc", "lib/python3.12/multiprocessing/popen_spawn_win32.pyc", "lib/python3.12/multiprocessing/process.pyc", "lib/python3.12/multiprocessing/queues.pyc", "lib/python3.12/multiprocessing/reduction.pyc", "lib/python3.12/multiprocessing/resource_sharer.pyc", "lib/python3.12/multiprocessing/resource_tracker.pyc", "lib/python3.12/multiprocessing/shared_memory.pyc", "lib/python3.12/multiprocessing/sharedctypes.pyc", "lib/python3.12/multiprocessing/spawn.pyc", "lib/python3.12/multiprocessing/synchronize.pyc", "lib/python3.12/multiprocessing/util.pyc", "lib/python3.12/netrc.pyc", "lib/python3.12/ntpath.pyc", "lib/python3.12/nturl2path.pyc", "lib/python3.12/numbers.pyc", "lib/python3.12/opcode.pyc", "lib/python3.12/operator.pyc", "lib/python3.12/optparse.pyc", "lib/python3.12/ordlookup/__init__.pyc", "lib/python3.12/ordlookup/oleaut32.pyc", "lib/python3.12/ordlookup/ws2_32.pyc", "lib/python3.12/os.pyc", "lib/python3.12/past/__init__.pyc", "lib/python3.12/past/builtins/__init__.pyc", "lib/python3.12/past/builtins/misc.pyc", "lib/python3.12/past/builtins/noniterators.pyc", "lib/python3.12/past/translation/__init__.pyc", "lib/python3.12/past/types/__init__.pyc", "lib/python3.12/past/types/basestring.pyc", "lib/python3.12/past/types/olddict.pyc", "lib/python3.12/past/types/oldstr.pyc", "lib/python3.12/past/utils/__init__.pyc", "lib/python3.12/pathlib.pyc", "lib/python3.12/pdb.pyc", "lib/python3.12/pefile.pyc", "lib/python3.12/pickle.pyc", "lib/python3.12/pickletools.pyc", "lib/python3.12/pipes.pyc", "lib/python3.12/pkgutil.pyc", "lib/python3.12/platform.pyc", "lib/python3.12/plistlib.pyc", "lib/python3.12/poplib.pyc", "lib/python3.12/posixpath.pyc", "lib/python3.12/pprint.pyc", "lib/python3.12/profile.pyc", "lib/python3.12/pstats.pyc", "lib/python3.12/pty.pyc", "lib/python3.12/py_compile.pyc", "lib/python3.12/pyasn1/__init__.pyc", "lib/python3.12/pyasn1/codec/__init__.pyc", "lib/python3.12/pyasn1/codec/ber/__init__.pyc", "lib/python3.12/pyasn1/codec/ber/decoder.pyc", "lib/python3.12/pyasn1/codec/ber/encoder.pyc", "lib/python3.12/pyasn1/codec/ber/eoo.pyc", "lib/python3.12/pyasn1/codec/cer/__init__.pyc", "lib/python3.12/pyasn1/codec/cer/decoder.pyc", "lib/python3.12/pyasn1/codec/cer/encoder.pyc", "lib/python3.12/pyasn1/codec/der/__init__.pyc", "lib/python3.12/pyasn1/codec/der/decoder.pyc", "lib/python3.12/pyasn1/codec/der/encoder.pyc", "lib/python3.12/pyasn1/codec/native/__init__.pyc", "lib/python3.12/pyasn1/codec/native/decoder.pyc", "lib/python3.12/pyasn1/codec/native/encoder.pyc", "lib/python3.12/pyasn1/codec/streaming.pyc", "lib/python3.12/pyasn1/compat/__init__.pyc", "lib/python3.12/pyasn1/compat/integer.pyc", "lib/python3.12/pyasn1/debug.pyc", "lib/python3.12/pyasn1/error.pyc", "lib/python3.12/pyasn1/type/__init__.pyc", "lib/python3.12/pyasn1/type/base.pyc", "lib/python3.12/pyasn1/type/char.pyc", "lib/python3.12/pyasn1/type/constraint.pyc", "lib/python3.12/pyasn1/type/error.pyc", "lib/python3.12/pyasn1/type/namedtype.pyc", "lib/python3.12/pyasn1/type/namedval.pyc", "lib/python3.12/pyasn1/type/opentype.pyc", "lib/python3.12/pyasn1/type/tag.pyc", "lib/python3.12/pyasn1/type/tagmap.pyc", "lib/python3.12/pyasn1/type/univ.pyc", "lib/python3.12/pyasn1/type/useful.pyc", "lib/python3.12/pyclbr.pyc", "lib/python3.12/pydoc.pyc", "lib/python3.12/pygame_sdl2/__init__.pyc", "lib/python3.12/pygame_sdl2/compat.pyc", "lib/python3.12/pygame_sdl2/sprite.pyc", "lib/python3.12/pygame_sdl2/sysfont.pyc", "lib/python3.12/pygame_sdl2/threads/Py25Queue.pyc", "lib/python3.12/pygame_sdl2/threads/__init__.pyc", "lib/python3.12/pygame_sdl2/time.pyc", "lib/python3.12/pygame_sdl2/version.pyc", "lib/python3.12/pyobjus/__init__.pyc", "lib/python3.12/pyobjus/dylib_manager.pyc", "lib/python3.12/pyobjus/objc_py_types.pyc", "lib/python3.12/pyobjus/protocols.pyc", "lib/python3.12/queue.pyc", "lib/python3.12/quopri.pyc", "lib/python3.12/random.pyc", "lib/python3.12/re/__init__.pyc", "lib/python3.12/re/_casefix.pyc", "lib/python3.12/re/_compiler.pyc", "lib/python3.12/re/_constants.pyc", "lib/python3.12/re/_parser.pyc", "lib/python3.12/reprlib.pyc", "lib/python3.12/requests/__init__.pyc", "lib/python3.12/requests/__version__.pyc", "lib/python3.12/requests/_internal_utils.pyc", "lib/python3.12/requests/adapters.pyc", "lib/python3.12/requests/api.pyc", "lib/python3.12/requests/auth.pyc", "lib/python3.12/requests/certs.pyc", "lib/python3.12/requests/compat.pyc", "lib/python3.12/requests/cookies.pyc", "lib/python3.12/requests/exceptions.pyc", "lib/python3.12/requests/help.pyc", "lib/python3.12/requests/hooks.pyc", "lib/python3.12/requests/models.pyc", "lib/python3.12/requests/packages.pyc", "lib/python3.12/requests/sessions.pyc", "lib/python3.12/requests/status_codes.pyc", "lib/python3.12/requests/structures.pyc", "lib/python3.12/requests/utils.pyc", "lib/python3.12/rlcompleter.pyc", "lib/python3.12/rsa/__init__.pyc", "lib/python3.12/rsa/asn1.pyc", "lib/python3.12/rsa/cli.pyc", "lib/python3.12/rsa/common.pyc", "lib/python3.12/rsa/core.pyc", "lib/python3.12/rsa/key.pyc", "lib/python3.12/rsa/parallel.pyc", "lib/python3.12/rsa/pem.pyc", "lib/python3.12/rsa/pkcs1.pyc", "lib/python3.12/rsa/pkcs1_v2.pyc", "lib/python3.12/rsa/prime.pyc", "lib/python3.12/rsa/randnum.pyc", "lib/python3.12/rsa/transform.pyc", "lib/python3.12/rsa/util.pyc", "lib/python3.12/runpy.pyc", "lib/python3.12/sched.pyc", "lib/python3.12/secrets.pyc", "lib/python3.12/selectors.pyc", "lib/python3.12/shelve.pyc", "lib/python3.12/shlex.pyc", "lib/python3.12/shutil.pyc", "lib/python3.12/signal.pyc", "lib/python3.12/site.pyc", "lib/python3.12/sitecustomize.pyc", "lib/python3.12/six.pyc", "lib/python3.12/socket.pyc", "lib/python3.12/socketserver.pyc", "lib/python3.12/socks.pyc", "lib/python3.12/sre_compile.pyc", "lib/python3.12/sre_constants.pyc", "lib/python3.12/sre_parse.pyc", "lib/python3.12/ssl.pyc", "lib/python3.12/stat.pyc", "lib/python3.12/statistics.pyc", "lib/python3.12/steamapi.pyc", "lib/python3.12/string.pyc", "lib/python3.12/stringprep.pyc", "lib/python3.12/struct.pyc", "lib/python3.12/subprocess.pyc", "lib/python3.12/sunau.pyc", "lib/python3.12/symtable.pyc", "lib/python3.12/sysconfig.pyc", "lib/python3.12/tabnanny.pyc", "lib/python3.12/tarfile.pyc", "lib/python3.12/tempfile.pyc", "lib/python3.12/textwrap.pyc", "lib/python3.12/this.pyc", "lib/python3.12/threading.pyc", "lib/python3.12/timeit.pyc", "lib/python3.12/token.pyc", "lib/python3.12/tokenize.pyc", "lib/python3.12/trace.pyc", "lib/python3.12/traceback.pyc", "lib/python3.12/tracemalloc.pyc", "lib/python3.12/tty.pyc", "lib/python3.12/types.pyc", "lib/python3.12/typing.pyc", "lib/python3.12/urllib/__init__.pyc", "lib/python3.12/urllib/error.pyc", "lib/python3.12/urllib/parse.pyc", "lib/python3.12/urllib/request.pyc", "lib/python3.12/urllib/response.pyc", "lib/python3.12/urllib/robotparser.pyc", "lib/python3.12/urllib3/__init__.pyc", "lib/python3.12/urllib3/_base_connection.pyc", "lib/python3.12/urllib3/_collections.pyc", "lib/python3.12/urllib3/_request_methods.pyc", "lib/python3.12/urllib3/_version.pyc", "lib/python3.12/urllib3/connection.pyc", "lib/python3.12/urllib3/connectionpool.pyc", "lib/python3.12/urllib3/contrib/__init__.pyc", "lib/python3.12/urllib3/contrib/emscripten/__init__.pyc", "lib/python3.12/urllib3/contrib/emscripten/connection.pyc", "lib/python3.12/urllib3/contrib/emscripten/fetch.pyc", "lib/python3.12/urllib3/contrib/emscripten/request.pyc", "lib/python3.12/urllib3/contrib/emscripten/response.pyc", "lib/python3.12/urllib3/contrib/pyopenssl.pyc", "lib/python3.12/urllib3/contrib/socks.pyc", "lib/python3.12/urllib3/exceptions.pyc", "lib/python3.12/urllib3/fields.pyc", "lib/python3.12/urllib3/filepost.pyc", "lib/python3.12/urllib3/http2.pyc", "lib/python3.12/urllib3/poolmanager.pyc", "lib/python3.12/urllib3/response.pyc", "lib/python3.12/urllib3/util/__init__.pyc", "lib/python3.12/urllib3/util/connection.pyc", "lib/python3.12/urllib3/util/proxy.pyc", "lib/python3.12/urllib3/util/request.pyc", "lib/python3.12/urllib3/util/response.pyc", "lib/python3.12/urllib3/util/retry.pyc", "lib/python3.12/urllib3/util/ssl_.pyc", "lib/python3.12/urllib3/util/ssl_match_hostname.pyc", "lib/python3.12/urllib3/util/ssltransport.pyc", "lib/python3.12/urllib3/util/timeout.pyc", "lib/python3.12/urllib3/util/url.pyc", "lib/python3.12/urllib3/util/util.pyc", "lib/python3.12/urllib3/util/wait.pyc", "lib/python3.12/uu.pyc", "lib/python3.12/uuid.pyc", "lib/python3.12/warnings.pyc", "lib/python3.12/wave.pyc", "lib/python3.12/weakref.pyc", "lib/python3.12/webbrowser.pyc", "lib/python3.12/websockets/__init__.pyc", "lib/python3.12/websockets/__main__.pyc", "lib/python3.12/websockets/auth.pyc", "lib/python3.12/websockets/client.pyc", "lib/python3.12/websockets/connection.pyc", "lib/python3.12/websockets/datastructures.pyc", "lib/python3.12/websockets/exceptions.pyc", "lib/python3.12/websockets/extensions/__init__.pyc", "lib/python3.12/websockets/extensions/base.pyc", "lib/python3.12/websockets/extensions/permessage_deflate.pyc", "lib/python3.12/websockets/frames.pyc", "lib/python3.12/websockets/headers.pyc", "lib/python3.12/websockets/http.pyc", "lib/python3.12/websockets/http11.pyc", "lib/python3.12/websockets/imports.pyc", "lib/python3.12/websockets/legacy/__init__.pyc", "lib/python3.12/websockets/legacy/async_timeout.pyc", "lib/python3.12/websockets/legacy/auth.pyc", "lib/python3.12/websockets/legacy/client.pyc", "lib/python3.12/websockets/legacy/compatibility.pyc", "lib/python3.12/websockets/legacy/framing.pyc", "lib/python3.12/websockets/legacy/handshake.pyc", "lib/python3.12/websockets/legacy/http.pyc", "lib/python3.12/websockets/legacy/protocol.pyc", "lib/python3.12/websockets/legacy/server.pyc", "lib/python3.12/websockets/protocol.pyc", "lib/python3.12/websockets/server.pyc", "lib/python3.12/websockets/streams.pyc", "lib/python3.12/websockets/sync/__init__.pyc", "lib/python3.12/websockets/sync/client.pyc", "lib/python3.12/websockets/sync/connection.pyc", "lib/python3.12/websockets/sync/messages.pyc", "lib/python3.12/websockets/sync/server.pyc", "lib/python3.12/websockets/sync/utils.pyc", "lib/python3.12/websockets/typing.pyc", "lib/python3.12/websockets/uri.pyc", "lib/python3.12/websockets/utils.pyc", "lib/python3.12/websockets/version.pyc", "lib/python3.12/xml/__init__.pyc", "lib/python3.12/xml/dom/NodeFilter.pyc", "lib/python3.12/xml/dom/__init__.pyc", "lib/python3.12/xml/dom/domreg.pyc", "lib/python3.12/xml/dom/expatbuilder.pyc", "lib/python3.12/xml/dom/minicompat.pyc", "lib/python3.12/xml/dom/minidom.pyc", "lib/python3.12/xml/dom/pulldom.pyc", "lib/python3.12/xml/dom/xmlbuilder.pyc", "lib/python3.12/xml/etree/ElementInclude.pyc", "lib/python3.12/xml/etree/ElementPath.pyc", "lib/python3.12/xml/etree/ElementTree.pyc", "lib/python3.12/xml/etree/__init__.pyc", "lib/python3.12/xml/etree/cElementTree.pyc", "lib/python3.12/xml/parsers/__init__.pyc", "lib/python3.12/xml/parsers/expat.pyc", "lib/python3.12/xml/sax/__init__.pyc", "lib/python3.12/xml/sax/_exceptions.pyc", "lib/python3.12/xml/sax/expatreader.pyc", "lib/python3.12/xml/sax/handler.pyc", "lib/python3.12/xml/sax/saxutils.pyc", "lib/python3.12/xml/sax/xmlreader.pyc", "lib/python3.12/zipapp.pyc", "lib/python3.12/zipfile/__init__.pyc", "lib/python3.12/zipfile/__main__.pyc", "lib/python3.12/zipfile/_path/__init__.pyc", "lib/python3.12/zipfile/_path/glob.pyc", "lib/python3.12/zipimport.pyc", "lib/python3.12/zoneinfo/__init__.pyc", "lib/python3.12/zoneinfo/_common.pyc", "lib/python3.12/zoneinfo/_tzpath.pyc", "lib/python3.12/zoneinfo/_zoneinfo.pyc", "renpy.app/Contents/CodeResources", "renpy.app/Contents/Info.plist", "renpy.app/Contents/MacOS/librenpython.dylib", "renpy.app/Contents/MacOS/python", "renpy.app/Contents/MacOS/pythonw", "renpy.app/Contents/MacOS/renpy", "renpy.app/Contents/MacOS/zsync", "renpy.app/Contents/MacOS/zsyncmake", "renpy.app/Contents/Resources/icon.icns", "renpy.app/Contents/_CodeSignature/CodeResources", "renpy.exe", "renpy.py", "renpy.sh", "renpy/__init__.py", "renpy/__pycache__/__init__.cpython-312.pyc", "renpy/__pycache__/add_from.cpython-312.pyc", "renpy/__pycache__/arguments.cpython-312.pyc", "renpy/__pycache__/ast.cpython-312.pyc", "renpy/__pycache__/atl.cpython-312.pyc", "renpy/__pycache__/bootstrap.cpython-312.pyc", "renpy/__pycache__/character.cpython-312.pyc", "renpy/__pycache__/color.cpython-312.pyc", "renpy/__pycache__/config.cpython-312.pyc", "renpy/__pycache__/curry.cpython-312.pyc", "renpy/__pycache__/debug.cpython-312.pyc", "renpy/__pycache__/defaultstore.cpython-312.pyc", "renpy/__pycache__/dump.cpython-312.pyc", "renpy/__pycache__/easy.cpython-312.pyc", "renpy/__pycache__/editor.cpython-312.pyc", "renpy/__pycache__/error.cpython-312.pyc", "renpy/__pycache__/execution.cpython-312.pyc", "renpy/__pycache__/game.cpython-312.pyc", "renpy/__pycache__/importer.cpython-312.pyc", "renpy/__pycache__/lexer.cpython-312.pyc", "renpy/__pycache__/lint.cpython-312.pyc", "renpy/__pycache__/loader.cpython-312.pyc", "renpy/__pycache__/loadsave.cpython-312.pyc", "renpy/__pycache__/log.cpython-312.pyc", "renpy/__pycache__/main.cpython-312.pyc", "renpy/__pycache__/memory.cpython-312.pyc", "renpy/__pycache__/minstore.cpython-312.pyc", "renpy/__pycache__/object.cpython-312.pyc", "renpy/__pycache__/parameter.cpython-312.pyc", "renpy/__pycache__/parser.cpython-312.pyc", "renpy/__pycache__/performance.cpython-312.pyc", "renpy/__pycache__/persistent.cpython-312.pyc", "renpy/__pycache__/preferences.cpython-312.pyc", "renpy/__pycache__/py3analysis.cpython-312.pyc", "renpy/__pycache__/pyanalysis.cpython-312.pyc", "renpy/__pycache__/python.cpython-312.pyc", "renpy/__pycache__/revertable.cpython-312.pyc", "renpy/__pycache__/rollback.cpython-312.pyc", "renpy/__pycache__/savelocation.cpython-312.pyc", "renpy/__pycache__/savetoken.cpython-312.pyc", "renpy/__pycache__/screenlang.cpython-312.pyc", "renpy/__pycache__/script.cpython-312.pyc", "renpy/__pycache__/scriptedit.cpython-312.pyc", "renpy/__pycache__/statements.cpython-312.pyc", "renpy/__pycache__/substitutions.cpython-312.pyc", "renpy/__pycache__/ui.cpython-312.pyc", "renpy/__pycache__/util.cpython-312.pyc", "renpy/__pycache__/vc_version.cpython-312.pyc", "renpy/__pycache__/versions.cpython-312.pyc", "renpy/__pycache__/warp.cpython-312.pyc", "renpy/__pycache__/webloader.cpython-312.pyc", "renpy/add_from.py", "renpy/arguments.py", "renpy/ast.py", "renpy/atl.py", "renpy/audio/__init__.py", "renpy/audio/__pycache__/__init__.cpython-312.pyc", "renpy/audio/__pycache__/audio.cpython-312.pyc", "renpy/audio/__pycache__/music.cpython-312.pyc", "renpy/audio/__pycache__/sound.cpython-312.pyc", "renpy/audio/__pycache__/webaudio.cpython-312.pyc", "renpy/audio/audio.py", "renpy/audio/music.py", "renpy/audio/sound.py", "renpy/audio/webaudio.py", "renpy/bootstrap.py", "renpy/character.py", "renpy/color.py", "renpy/common/000atl.rpy", "renpy/common/000atl.rpyc", "renpy/common/000namespaces.rpy", "renpy/common/000namespaces.rpyc", "renpy/common/000statements.rpy", "renpy/common/000statements.rpyc", "renpy/common/000window.rpy", "renpy/common/000window.rpyc", "renpy/common/00accessibility.rpy", "renpy/common/00accessibility.rpyc", "renpy/common/00achievement.rpy", "renpy/common/00achievement.rpyc", "renpy/common/00action_audio.rpy", "renpy/common/00action_audio.rpyc", "renpy/common/00action_control.rpy", "renpy/common/00action_control.rpyc", "renpy/common/00action_data.rpy", "renpy/common/00action_data.rpyc", "renpy/common/00action_file.rpy", "renpy/common/00action_file.rpyc", "renpy/common/00action_menu.rpy", "renpy/common/00action_menu.rpyc", "renpy/common/00action_other.rpy", "renpy/common/00action_other.rpyc", "renpy/common/00audio.rpy", "renpy/common/00audio.rpyc", "renpy/common/00barvalues.rpy", "renpy/common/00barvalues.rpyc", "renpy/common/00build.rpy", "renpy/common/00build.rpyc", "renpy/common/00compat.rpy", "renpy/common/00compat.rpyc", "renpy/common/00console.rpy", "renpy/common/00console.rpyc", "renpy/common/00db.rpyc", "renpy/common/00db_ren.py", "renpy/common/00defaults.rpy", "renpy/common/00defaults.rpyc", "renpy/common/00definitions.rpy", "renpy/common/00definitions.rpyc", "renpy/common/00director.rpy", "renpy/common/00director.rpyc", "renpy/common/00gallery.rpy", "renpy/common/00gallery.rpyc", "renpy/common/00gamemenu.rpy", "renpy/common/00gamemenu.rpyc", "renpy/common/00gamepad.rpy", "renpy/common/00gamepad.rpyc", "renpy/common/00gltest.rpy", "renpy/common/00gltest.rpyc", "renpy/common/00gui.rpy", "renpy/common/00gui.rpyc", "renpy/common/00iap.rpy", "renpy/common/00iap.rpyc", "renpy/common/00icon.rpy", "renpy/common/00icon.rpyc", "renpy/common/00iconbutton.rpy", "renpy/common/00iconbutton.rpyc", "renpy/common/00images.rpy", "renpy/common/00images.rpyc", "renpy/common/00inputvalues.rpy", "renpy/common/00inputvalues.rpyc", "renpy/common/00keymap.rpy", "renpy/common/00keymap.rpyc", "renpy/common/00layeredimage.rpyc", "renpy/common/00layeredimage_ren.py", "renpy/common/00layout.rpy", "renpy/common/00layout.rpyc", "renpy/common/00library.rpy", "renpy/common/00library.rpyc", "renpy/common/00matrixcolor.rpy", "renpy/common/00matrixcolor.rpyc", "renpy/common/00matrixtransform.rpy", "renpy/common/00matrixtransform.rpyc", "renpy/common/00mixers.rpy", "renpy/common/00mixers.rpyc", "renpy/common/00mousedisplayable.rpy", "renpy/common/00mousedisplayable.rpyc", "renpy/common/00musicroom.rpy", "renpy/common/00musicroom.rpyc", "renpy/common/00nvl_mode.rpy", "renpy/common/00nvl_mode.rpyc", "renpy/common/00obsolete.rpy", "renpy/common/00obsolete.rpyc", "renpy/common/00performance.rpy", "renpy/common/00performance.rpyc", "renpy/common/00placeholder.rpy", "renpy/common/00placeholder.rpyc", "renpy/common/00preferences.rpy", "renpy/common/00preferences.rpyc", "renpy/common/00shaders.rpy", "renpy/common/00shaders.rpyc", "renpy/common/00sideimage.rpy", "renpy/common/00sideimage.rpyc", "renpy/common/00speechbubble.rpy", "renpy/common/00speechbubble.rpyc", "renpy/common/00splines.rpy", "renpy/common/00splines.rpyc", "renpy/common/00sshtransition.rpyc", "renpy/common/00sshtransition_ren.py", "renpy/common/00start.rpy", "renpy/common/00start.rpyc", "renpy/common/00steam.rpy", "renpy/common/00steam.rpyc", "renpy/common/00style.rpy", "renpy/common/00style.rpyc", "renpy/common/00stylepreferences.rpy", "renpy/common/00stylepreferences.rpyc", "renpy/common/00sync.rpy", "renpy/common/00sync.rpyc", "renpy/common/00textshader.rpyc", "renpy/common/00textshader_ren.py", "renpy/common/00themes.rpy", "renpy/common/00themes.rpyc", "renpy/common/00touchkeyboard.rpy", "renpy/common/00touchkeyboard.rpyc", "renpy/common/00translation.rpy", "renpy/common/00translation.rpyc", "renpy/common/00updater.rpy", "renpy/common/00updater.rpyc", "renpy/common/00voice.rpy", "renpy/common/00voice.rpyc", "renpy/common/DejaVuSans-Bold.ttf", "renpy/common/DejaVuSans.ttf", "renpy/common/DejaVuSans.txt", "renpy/common/TwemojiCOLRv0.ttf", "renpy/common/TwemojiCOLRv0.txt", "renpy/common/_OpenDyslexic3-Regular.ttf", "renpy/common/_OpenDyslexic3-Regular.txt", "renpy/common/__pycache__/00db_ren.cpython-312.pyc", "renpy/common/__pycache__/00layeredimage_ren.cpython-312.pyc", "renpy/common/__pycache__/00sshtransition_ren.cpython-312.pyc", "renpy/common/__pycache__/00textshader_ren.cpython-312.pyc", "renpy/common/_audio.js", "renpy/common/_audio_filter.js", "renpy/common/_compat/gamemenu.rpym", "renpy/common/_compat/gamemenu.rpymc", "renpy/common/_compat/library.rpym", "renpy/common/_compat/library.rpymc", "renpy/common/_compat/mainmenu.rpym", "renpy/common/_compat/mainmenu.rpymc", "renpy/common/_compat/preferences.rpym", "renpy/common/_compat/preferences.rpymc", "renpy/common/_compat/styles.rpym", "renpy/common/_compat/styles.rpymc", "renpy/common/_compat/themes.rpym", "renpy/common/_compat/themes.rpymc", "renpy/common/_developer/developer.rpym", "renpy/common/_developer/developer.rpymc", "renpy/common/_developer/inspector.rpym", "renpy/common/_developer/inspector.rpymc", "renpy/common/_dl_silence.ogg", "renpy/common/_errorhandling.rpym", "renpy/common/_errorhandling.rpymc", "renpy/common/_layout/classic_joystick_preferences.rpym", "renpy/common/_layout/classic_joystick_preferences.rpymc", "renpy/common/_layout/classic_load_save.rpym", "renpy/common/_layout/classic_load_save.rpymc", "renpy/common/_layout/classic_main_menu.rpym", "renpy/common/_layout/classic_main_menu.rpymc", "renpy/common/_layout/classic_navigation.rpym", "renpy/common/_layout/classic_navigation.rpymc", "renpy/common/_layout/classic_preferences.rpym", "renpy/common/_layout/classic_preferences.rpymc", "renpy/common/_layout/classic_preferences_common.rpym", "renpy/common/_layout/classic_preferences_common.rpymc", "renpy/common/_layout/classic_yesno_prompt.rpym", "renpy/common/_layout/classic_yesno_prompt.rpymc", "renpy/common/_layout/grouped_main_menu.rpym", "renpy/common/_layout/grouped_navigation.rpym", "renpy/common/_layout/grouped_navigation.rpymc", "renpy/common/_layout/imagemap_common.rpym", "renpy/common/_layout/imagemap_common.rpymc", "renpy/common/_layout/imagemap_load_save.rpym", "renpy/common/_layout/imagemap_load_save.rpymc", "renpy/common/_layout/imagemap_main_menu.rpym", "renpy/common/_layout/imagemap_main_menu.rpymc", "renpy/common/_layout/imagemap_navigation.rpym", "renpy/common/_layout/imagemap_navigation.rpymc", "renpy/common/_layout/imagemap_preferences.rpym", "renpy/common/_layout/imagemap_preferences.rpymc", "renpy/common/_layout/imagemap_yesno_prompt.rpym", "renpy/common/_layout/imagemap_yesno_prompt.rpymc", "renpy/common/_layout/one_column_preferences.rpym", "renpy/common/_layout/screen_joystick_preferences.rpym", "renpy/common/_layout/screen_joystick_preferences.rpymc", "renpy/common/_layout/screen_load_save.rpym", "renpy/common/_layout/screen_load_save.rpymc", "renpy/common/_layout/screen_main_menu.rpym", "renpy/common/_layout/screen_main_menu.rpymc", "renpy/common/_layout/screen_preferences.rpym", "renpy/common/_layout/screen_preferences.rpymc", "renpy/common/_layout/screen_yesno_prompt.rpym", "renpy/common/_layout/screen_yesno_prompt.rpymc", "renpy/common/_layout/scrolling_load_save.rpym", "renpy/common/_layout/scrolling_load_save.rpymc", "renpy/common/_layout/two_column_preferences.rpym", "renpy/common/_layout/two_column_preferences.rpymc", "renpy/common/_missing_image.png", "renpy/common/_outline/bar.png", "renpy/common/_outline/circle.png", "renpy/common/_outline/vbar.png", "renpy/common/_placeholder/boy.png", "renpy/common/_placeholder/girl.png", "renpy/common/_roundrect/rr12.png", "renpy/common/_roundrect/rr12g.png", "renpy/common/_roundrect/rr6.png", "renpy/common/_roundrect/rr6g.png", "renpy/common/_roundrect/rrscrollbar.png", "renpy/common/_roundrect/rrscrollbar_thumb.png", "renpy/common/_roundrect/rrslider_empty.png", "renpy/common/_roundrect/rrslider_full.png", "renpy/common/_roundrect/rrslider_thumb.png", "renpy/common/_roundrect/rrvscrollbar.png", "renpy/common/_roundrect/rrvscrollbar_thumb.png", "renpy/common/_roundrect/rrvslider_empty.png", "renpy/common/_roundrect/rrvslider_full.png", "renpy/common/_roundrect/rrvslider_thumb.png", "renpy/common/_shaders.rpym", "renpy/common/_shaders.rpymc", "renpy/common/_silence.ogg", "renpy/common/_theme_amie2/bar.png", "renpy/common/_theme_amie2/button.png", "renpy/common/_theme_amie2/button_hover.png", "renpy/common/_theme_amie2/frame.png", "renpy/common/_theme_amie2/hover_bar.png", "renpy/common/_theme_amie2/hover_frame.png", "renpy/common/_theme_austen/au_box.png", "renpy/common/_theme_austen/auscrollbar.png", "renpy/common/_theme_austen/auscrollbar_thumb.png", "renpy/common/_theme_austen/auslider_empty.png", "renpy/common/_theme_austen/auslider_full.png", "renpy/common/_theme_austen/auslider_thumb.png", "renpy/common/_theme_austen/auvscrollbar.png", "renpy/common/_theme_austen/auvscrollbar_thumb.png", "renpy/common/_theme_austen/auvslider_empty.png", "renpy/common/_theme_austen/auvslider_full.png", "renpy/common/_theme_austen/auvslider_thumb.png", "renpy/common/_theme_awt/OFL.txt", "renpy/common/_theme_awt/Quicksand-Bold.ttf", "renpy/common/_theme_awt/Quicksand-Regular.ttf", "renpy/common/_theme_awt/bar_full.png", "renpy/common/_theme_awt/bar_full_overlay.png", "renpy/common/_theme_awt/bar_thumb.gif", "renpy/common/_theme_awt/bar_thumb.png", "renpy/common/_theme_awt/bar_thumb_overlay.png", "renpy/common/_theme_awt/button.png", "renpy/common/_theme_awt/button_disabled_overlay.png", "renpy/common/_theme_awt/button_overlay.png", "renpy/common/_theme_awt/button_overlay_highlight.png", "renpy/common/_theme_awt/button_selected.png", "renpy/common/_theme_awt/button_selected_overlay.png", "renpy/common/_theme_awt/button_selected_overlay_highlight.png", "renpy/common/_theme_awt/frame.png", "renpy/common/_theme_awt/frame_overlay.png", "renpy/common/_theme_awt/radio_base.png", "renpy/common/_theme_awt/radio_base_overlay.png", "renpy/common/_theme_awt/radio_selected_hover.png", "renpy/common/_theme_awt/radio_unselected.png", "renpy/common/_theme_awt/radio_unselected_hover.png", "renpy/common/_theme_awt/scroller.png", "renpy/common/_theme_awt/scroller_overlay.png", "renpy/common/_theme_awt/slider_empty_all.png", "renpy/common/_theme_awt/slider_empty_overlay.png", "renpy/common/_theme_awt/slider_full.png", "renpy/common/_theme_awt/slider_full_overlay.png", "renpy/common/_theme_awt/v_bar_full.png", "renpy/common/_theme_awt/v_bar_full_overlay.png", "renpy/common/_theme_awt/v_bar_thumb.png", "renpy/common/_theme_awt/v_bar_thumb_overlay.png", "renpy/common/_theme_awt/vscroller.png", "renpy/common/_theme_awt/vscroller_overlay.png", "renpy/common/_theme_awt/vslider_empty_all.png", "renpy/common/_theme_awt/vslider_full.png", "renpy/common/_theme_awt/vslider_full_overlay.png", "renpy/common/_theme_awt/vthumb.png", "renpy/common/_theme_awt/vthumb_overlay.png", "renpy/common/_theme_bordered/br_box.png", "renpy/common/_theme_bordered/brscrollbar.png", "renpy/common/_theme_bordered/brscrollbar_thumb.png", "renpy/common/_theme_bordered/brslider_empty.png", "renpy/common/_theme_bordered/brslider_full.png", "renpy/common/_theme_bordered/brslider_thumb.png", "renpy/common/_theme_bordered/brvscrollbar.png", "renpy/common/_theme_bordered/brvscrollbar_thumb.png", "renpy/common/_theme_bordered/brvslider_empty.png", "renpy/common/_theme_bordered/brvslider_full.png", "renpy/common/_theme_bordered/brvslider_thumb.png", "renpy/common/_theme_crayon/cry_box.png", "renpy/common/_theme_crayon/cry_box2.png", "renpy/common/_theme_crayon/cryscrollbar.png", "renpy/common/_theme_crayon/cryscrollbar_thumb.png", "renpy/common/_theme_crayon/cryslider_empty.png", "renpy/common/_theme_crayon/cryslider_full.png", "renpy/common/_theme_crayon/cryslider_thumb.png", "renpy/common/_theme_crayon/cryvscrollbar.png", "renpy/common/_theme_crayon/cryvscrollbar_thumb.png", "renpy/common/_theme_crayon/cryvslider_empty.png", "renpy/common/_theme_crayon/cryvslider_full.png", "renpy/common/_theme_crayon/cryvslider_thumb.png", "renpy/common/_theme_crayon/rr12g.png", "renpy/common/_theme_diamond/d_box.png", "renpy/common/_theme_diamond/dscrollbar.png", "renpy/common/_theme_diamond/dscrollbar_thumb.png", "renpy/common/_theme_diamond/dslider_empty.png", "renpy/common/_theme_diamond/dslider_full.png", "renpy/common/_theme_diamond/dslider_thumb.png", "renpy/common/_theme_diamond/dvscrollbar.png", "renpy/common/_theme_diamond/dvscrollbar_thumb.png", "renpy/common/_theme_diamond/dvslider_empty.png", "renpy/common/_theme_diamond/dvslider_full.png", "renpy/common/_theme_diamond/dvslider_thumb.png", "renpy/common/_theme_glow/g_box.png", "renpy/common/_theme_glow/g_outline.png", "renpy/common/_theme_glow/gscrollbar.png", "renpy/common/_theme_glow/gscrollbar_thumb.png", "renpy/common/_theme_glow/gslider_empty.png", "renpy/common/_theme_glow/gslider_full.png", "renpy/common/_theme_glow/gslider_thumb.png", "renpy/common/_theme_glow/gvscrollbar.png", "renpy/common/_theme_glow/gvscrollbar_thumb.png", "renpy/common/_theme_glow/gvslider_empty.png", "renpy/common/_theme_glow/gvslider_full.png", "renpy/common/_theme_glow/gvslider_thumb.png", "renpy/common/_theme_marker/ink_box.png", "renpy/common/_theme_marker/inkscrollbar.png", "renpy/common/_theme_marker/inkscrollbar_thumb.png", "renpy/common/_theme_marker/inkslider_empty.png", "renpy/common/_theme_marker/inkslider_full.png", "renpy/common/_theme_marker/inkslider_thumb.png", "renpy/common/_theme_marker/inkvscrollbar.png", "renpy/common/_theme_marker/inkvscrollbar_thumb.png", "renpy/common/_theme_marker/inkvslider_empty.png", "renpy/common/_theme_marker/inkvslider_full.png", "renpy/common/_theme_marker/inkvslider_thumb.png", "renpy/common/_theme_regal/re_box.png", "renpy/common/_theme_regal/rescrollbar.png", "renpy/common/_theme_regal/rescrollbar_thumb.png", "renpy/common/_theme_regal/reslider_empty.png", "renpy/common/_theme_regal/reslider_full.png", "renpy/common/_theme_regal/reslider_thumb.png", "renpy/common/_theme_regal/revscrollbar.png", "renpy/common/_theme_regal/revscrollbar_thumb.png", "renpy/common/_theme_regal/revslider_empty.png", "renpy/common/_theme_regal/revslider_full.png", "renpy/common/_theme_regal/revslider_thumb.png", "renpy/common/_theme_threeD/th_box.png", "renpy/common/_theme_threeD/thscrollbar.png", "renpy/common/_theme_threeD/thscrollbar_thumb.png", "renpy/common/_theme_threeD/thslider_empty.png", "renpy/common/_theme_threeD/thslider_full.png", "renpy/common/_theme_threeD/thslider_thumb.png", "renpy/common/_theme_threeD/thvscrollbar.png", "renpy/common/_theme_threeD/thvscrollbar_thumb.png", "renpy/common/_theme_threeD/thvslider_empty.png", "renpy/common/_theme_threeD/thvslider_full.png", "renpy/common/_theme_threeD/thvslider_thumb.png", "renpy/common/_theme_tv/t_box.png", "renpy/common/_theme_tv/tscrollbar.png", "renpy/common/_theme_tv/tscrollbar_thumb.png", "renpy/common/_theme_tv/tslider_empty.png", "renpy/common/_theme_tv/tslider_full.png", "renpy/common/_theme_tv/tslider_thumb.png", "renpy/common/_theme_tv/tvscrollbar.png", "renpy/common/_theme_tv/tvscrollbar_thumb.png", "renpy/common/_theme_tv/tvslider_empty.png", "renpy/common/_theme_tv/tvslider_full.png", "renpy/common/_theme_tv/tvslider_thumb.png", "renpy/common/_transparent_tile.png", "renpy/common/_tv_unsafe.png", "renpy/common/blindstile.png", "renpy/common/gamecontrollerdb.txt", "renpy/common/squarestile.png", "renpy/compat/__init__.py", "renpy/compat/__pycache__/__init__.cpython-312.pyc", "renpy/compat/__pycache__/fixes.cpython-312.pyc", "renpy/compat/__pycache__/pickle.cpython-312.pyc", "renpy/compat/fixes.py", "renpy/compat/pickle.py", "renpy/config.py", "renpy/curry.py", "renpy/debug.py", "renpy/defaultstore.py", "renpy/display/__init__.py", "renpy/display/__pycache__/__init__.cpython-312.pyc", "renpy/display/__pycache__/anim.cpython-312.pyc", "renpy/display/__pycache__/behavior.cpython-312.pyc", "renpy/display/__pycache__/controller.cpython-312.pyc", "renpy/display/__pycache__/core.cpython-312.pyc", "renpy/display/__pycache__/displayable.cpython-312.pyc", "renpy/display/__pycache__/dragdrop.cpython-312.pyc", "renpy/display/__pycache__/emulator.cpython-312.pyc", "renpy/display/__pycache__/error.cpython-312.pyc", "renpy/display/__pycache__/focus.cpython-312.pyc", "renpy/display/__pycache__/gesture.cpython-312.pyc", "renpy/display/__pycache__/im.cpython-312.pyc", "renpy/display/__pycache__/image.cpython-312.pyc", "renpy/display/__pycache__/imagelike.cpython-312.pyc", "renpy/display/__pycache__/imagemap.cpython-312.pyc", "renpy/display/__pycache__/joystick.cpython-312.pyc", "renpy/display/__pycache__/layout.cpython-312.pyc", "renpy/display/__pycache__/minigame.cpython-312.pyc", "renpy/display/__pycache__/model.cpython-312.pyc", "renpy/display/__pycache__/module.cpython-312.pyc", "renpy/display/__pycache__/motion.cpython-312.pyc", "renpy/display/__pycache__/movetransition.cpython-312.pyc", "renpy/display/__pycache__/particle.cpython-312.pyc", "renpy/display/__pycache__/pgrender.cpython-312.pyc", "renpy/display/__pycache__/predict.cpython-312.pyc", "renpy/display/__pycache__/presplash.cpython-312.pyc", "renpy/display/__pycache__/scale.cpython-312.pyc", "renpy/display/__pycache__/scenelists.cpython-312.pyc", "renpy/display/__pycache__/screen.cpython-312.pyc", "renpy/display/__pycache__/swdraw.cpython-312.pyc", "renpy/display/__pycache__/transform.cpython-312.pyc", "renpy/display/__pycache__/transition.cpython-312.pyc", "renpy/display/__pycache__/tts.cpython-312.pyc", "renpy/display/__pycache__/video.cpython-312.pyc", "renpy/display/__pycache__/viewport.cpython-312.pyc", "renpy/display/anim.py", "renpy/display/behavior.py", "renpy/display/controller.py", "renpy/display/core.py", "renpy/display/displayable.py", "renpy/display/dragdrop.py", "renpy/display/emulator.py", "renpy/display/error.py", "renpy/display/focus.py", "renpy/display/gesture.py", "renpy/display/im.py", "renpy/display/image.py", "renpy/display/imagelike.py", "renpy/display/imagemap.py", "renpy/display/joystick.py", "renpy/display/layout.py", "renpy/display/matrix_functions.pxi", "renpy/display/minigame.py", "renpy/display/model.py", "renpy/display/module.py", "renpy/display/motion.py", "renpy/display/movetransition.py", "renpy/display/particle.py", "renpy/display/pgrender.py", "renpy/display/predict.py", "renpy/display/presplash.py", "renpy/display/scale.py", "renpy/display/scenelists.py", "renpy/display/screen.py", "renpy/display/swdraw.py", "renpy/display/transform.py", "renpy/display/transition.py", "renpy/display/tts.py", "renpy/display/video.py", "renpy/display/viewport.py", "renpy/dump.py", "renpy/easy.py", "renpy/editor.py", "renpy/error.py", "renpy/execution.py", "renpy/exports/__init__.py", "renpy/exports/__pycache__/__init__.cpython-312.pyc", "renpy/exports/__pycache__/actionexports.cpython-312.pyc", "renpy/exports/__pycache__/commonexports.cpython-312.pyc", "renpy/exports/__pycache__/contextexports.cpython-312.pyc", "renpy/exports/__pycache__/debugexports.cpython-312.pyc", "renpy/exports/__pycache__/displayexports.cpython-312.pyc", "renpy/exports/__pycache__/fetchexports.cpython-312.pyc", "renpy/exports/__pycache__/inputexports.cpython-312.pyc", "renpy/exports/__pycache__/loaderexports.cpython-312.pyc", "renpy/exports/__pycache__/mediaexports.cpython-312.pyc", "renpy/exports/__pycache__/menuexports.cpython-312.pyc", "renpy/exports/__pycache__/persistentexports.cpython-312.pyc", "renpy/exports/__pycache__/platformexports.cpython-312.pyc", "renpy/exports/__pycache__/predictexports.cpython-312.pyc", "renpy/exports/__pycache__/restartexports.cpython-312.pyc", "renpy/exports/__pycache__/rollbackexports.cpython-312.pyc", "renpy/exports/__pycache__/sayexports.cpython-312.pyc", "renpy/exports/__pycache__/scriptexports.cpython-312.pyc", "renpy/exports/__pycache__/statementexports.cpython-312.pyc", "renpy/exports/actionexports.py", "renpy/exports/commonexports.py", "renpy/exports/contextexports.py", "renpy/exports/debugexports.py", "renpy/exports/displayexports.py", "renpy/exports/fetchexports.py", "renpy/exports/inputexports.py", "renpy/exports/loaderexports.py", "renpy/exports/mediaexports.py", "renpy/exports/menuexports.py", "renpy/exports/persistentexports.py", "renpy/exports/platformexports.py", "renpy/exports/predictexports.py", "renpy/exports/restartexports.py", "renpy/exports/rollbackexports.py", "renpy/exports/sayexports.py", "renpy/exports/scriptexports.py", "renpy/exports/statementexports.py", "renpy/game.py", "renpy/gl2/__init__.py", "renpy/gl2/__pycache__/__init__.cpython-312.pyc", "renpy/gl2/__pycache__/gl2debug.cpython-312.pyc", "renpy/gl2/__pycache__/gl2functions.cpython-312.pyc", "renpy/gl2/__pycache__/gl2shadercache.cpython-312.pyc", "renpy/gl2/__pycache__/live2d.cpython-312.pyc", "renpy/gl2/__pycache__/live2dmotion.cpython-312.pyc", "renpy/gl2/gl2debug.py", "renpy/gl2/gl2functions.py", "renpy/gl2/gl2shadercache.py", "renpy/gl2/live2d.py", "renpy/gl2/live2dcsm.pxi", "renpy/gl2/live2dmotion.py", "renpy/importer.py", "renpy/lexer.py", "renpy/lint.py", "renpy/loader.py", "renpy/loadsave.py", "renpy/log.py", "renpy/main.py", "renpy/memory.py", "renpy/minstore.py", "renpy/object.py", "renpy/parameter.py", "renpy/parser.py", "renpy/performance.py", "renpy/persistent.py", "renpy/preferences.py", "renpy/pyanalysis.py", "renpy/python.py", "renpy/revertable.py", "renpy/rollback.py", "renpy/savelocation.py", "renpy/savetoken.py", "renpy/screenlang.py", "renpy/script.py", "renpy/scriptedit.py", "renpy/sl2/__init__.py", "renpy/sl2/__pycache__/__init__.cpython-312.pyc", "renpy/sl2/__pycache__/slast.cpython-312.pyc", "renpy/sl2/__pycache__/sldisplayables.cpython-312.pyc", "renpy/sl2/__pycache__/slparser.cpython-312.pyc", "renpy/sl2/__pycache__/slproperties.cpython-312.pyc", "renpy/sl2/slast.py", "renpy/sl2/sldisplayables.py", "renpy/sl2/slparser.py", "renpy/sl2/slproperties.py", "renpy/statements.py", "renpy/styledata/__init__.py", "renpy/styledata/__pycache__/__init__.cpython-312.pyc", "renpy/styledata/__pycache__/styleutil.cpython-312.pyc", "renpy/styledata/styleutil.py", "renpy/substitutions.py", "renpy/test/__init__.py", "renpy/test/__pycache__/__init__.cpython-312.pyc", "renpy/test/__pycache__/testast.cpython-312.pyc", "renpy/test/__pycache__/testexecution.cpython-312.pyc", "renpy/test/__pycache__/testfocus.cpython-312.pyc", "renpy/test/__pycache__/testkey.cpython-312.pyc", "renpy/test/__pycache__/testmouse.cpython-312.pyc", "renpy/test/__pycache__/testparser.cpython-312.pyc", "renpy/test/testast.py", "renpy/test/testexecution.py", "renpy/test/testfocus.py", "renpy/test/testkey.py", "renpy/test/testmouse.py", "renpy/test/testparser.py", "renpy/text/__init__.py", "renpy/text/__pycache__/__init__.cpython-312.pyc", "renpy/text/__pycache__/emoji_trie.cpython-312.pyc", "renpy/text/__pycache__/extras.cpython-312.pyc", "renpy/text/__pycache__/font.cpython-312.pyc", "renpy/text/__pycache__/shader.cpython-312.pyc", "renpy/text/__pycache__/text.cpython-312.pyc", "renpy/text/emoji_trie.py", "renpy/text/extras.py", "renpy/text/font.py", "renpy/text/linebreak.pxi", "renpy/text/shader.py", "renpy/text/text.py", "renpy/translation/__init__.py", "renpy/translation/__pycache__/__init__.cpython-312.pyc", "renpy/translation/__pycache__/dialogue.cpython-312.pyc", "renpy/translation/__pycache__/extract.cpython-312.pyc", "renpy/translation/__pycache__/generation.cpython-312.pyc", "renpy/translation/__pycache__/merge.cpython-312.pyc", "renpy/translation/__pycache__/scanstrings.cpython-312.pyc", "renpy/translation/dialogue.py", "renpy/translation/extract.py", "renpy/translation/generation.py", "renpy/translation/merge.py", "renpy/translation/scanstrings.py", "renpy/uguu/__init__.py", "renpy/uguu/__pycache__/__init__.cpython-312.pyc", "renpy/ui.py", "renpy/update/__init__.py", "renpy/update/__pycache__/__init__.cpython-312.pyc", "renpy/update/__pycache__/common.cpython-312.pyc", "renpy/update/__pycache__/deferred.cpython-312.pyc", "renpy/update/__pycache__/download.cpython-312.pyc", "renpy/update/__pycache__/generate.cpython-312.pyc", "renpy/update/__pycache__/update.cpython-312.pyc", "renpy/update/common.py", "renpy/update/deferred.py", "renpy/update/download.py", "renpy/update/generate.py", "renpy/update/update.py", "renpy/util.py", "renpy/vc_version.py", "renpy/versions.py", "renpy/warp.py", "renpy/webloader.py", "sdk-fonts/SourceHanSansLite.ttf", "the_question/android-icon_background.png", "the_question/android-icon_foreground.png", "the_question/android.json", "the_question/game/cache/bytecode-312.rpyb", "the_question/game/cache/py3analysis.rpyb", "the_question/game/cache/pyanalysis.rpyb", "the_question/game/cache/screens.rpyb", "the_question/game/cache/shaders.txt", "the_question/game/gui.rpy", "the_question/game/gui.rpyc", "the_question/game/gui/bar/bottom.png", "the_question/game/gui/bar/left.png", "the_question/game/gui/bar/right.png", "the_question/game/gui/bar/top.png", "the_question/game/gui/button/check_foreground.png", "the_question/game/gui/button/check_selected_foreground.png", "the_question/game/gui/button/choice_hover_background.png", "the_question/game/gui/button/choice_idle_background.png", "the_question/game/gui/button/hover_background.png", "the_question/game/gui/button/idle_background.png", "the_question/game/gui/button/quick_hover_background.png", "the_question/game/gui/button/quick_idle_background.png", "the_question/game/gui/button/radio_foreground.png", "the_question/game/gui/button/radio_selected_foreground.png", "the_question/game/gui/button/slot_hover_background.png", "the_question/game/gui/button/slot_idle_background.png", "the_question/game/gui/frame.png", "the_question/game/gui/game_menu.png", "the_question/game/gui/main_menu.png", "the_question/game/gui/namebox.png", "the_question/game/gui/notify.png", "the_question/game/gui/nvl.png", "the_question/game/gui/overlay/confirm.png", "the_question/game/gui/overlay/game_menu.png", "the_question/game/gui/overlay/main_menu.png", "the_question/game/gui/phone/nvl.png", "the_question/game/gui/phone/overlay/game_menu.png", "the_question/game/gui/phone/overlay/main_menu.png", "the_question/game/gui/phone/textbox.png", "the_question/game/gui/scrollbar/horizontal_hover_bar.png", "the_question/game/gui/scrollbar/horizontal_hover_thumb.png", "the_question/game/gui/scrollbar/horizontal_idle_bar.png", "the_question/game/gui/scrollbar/horizontal_idle_thumb.png", "the_question/game/gui/scrollbar/vertical_hover_bar.png", "the_question/game/gui/scrollbar/vertical_hover_thumb.png", "the_question/game/gui/scrollbar/vertical_idle_bar.png", "the_question/game/gui/scrollbar/vertical_idle_thumb.png", "the_question/game/gui/skip.png", "the_question/game/gui/slider/horizontal_hover_bar.png", "the_question/game/gui/slider/horizontal_hover_thumb.png", "the_question/game/gui/slider/horizontal_idle_bar.png", "the_question/game/gui/slider/horizontal_idle_thumb.png", "the_question/game/gui/slider/vertical_hover_bar.png", "the_question/game/gui/slider/vertical_hover_thumb.png", "the_question/game/gui/slider/vertical_idle_bar.png", "the_question/game/gui/slider/vertical_idle_thumb.png", "the_question/game/gui/textbox.png", "the_question/game/gui/window_icon.png", "the_question/game/illurock.opus", "the_question/game/images/bg club.jpg", "the_question/game/images/bg lecturehall.jpg", "the_question/game/images/bg meadow.jpg", "the_question/game/images/bg uni.jpg", "the_question/game/images/sylvie blue giggle.png", "the_question/game/images/sylvie blue normal.png", "the_question/game/images/sylvie blue smile.png", "the_question/game/images/sylvie blue surprised.png", "the_question/game/images/sylvie green giggle.png", "the_question/game/images/sylvie green normal.png", "the_question/game/images/sylvie green smile.png", "the_question/game/images/sylvie green surprised.png", "the_question/game/options.rpy", "the_question/game/options.rpyc", "the_question/game/screens.rpy", "the_question/game/screens.rpyc", "the_question/game/script.rpy", "the_question/game/script.rpyc", "the_question/game/tl/None/common.rpym", "the_question/game/tl/None/common.rpymc", "the_question/game/tl/czech/common.rpy", "the_question/game/tl/czech/common.rpyc", "the_question/game/tl/czech/options.rpy", "the_question/game/tl/czech/options.rpyc", "the_question/game/tl/czech/screens.rpy", "the_question/game/tl/czech/screens.rpyc", "the_question/game/tl/czech/script.rpy", "the_question/game/tl/czech/script.rpyc", "the_question/game/tl/danish/common.rpy", "the_question/game/tl/danish/common.rpyc", "the_question/game/tl/danish/options.rpy", "the_question/game/tl/danish/options.rpyc", "the_question/game/tl/danish/screens.rpy", "the_question/game/tl/danish/screens.rpyc", "the_question/game/tl/danish/script.rpy", "the_question/game/tl/danish/script.rpyc", "the_question/game/tl/french/common.rpy", "the_question/game/tl/french/common.rpyc", "the_question/game/tl/french/options.rpy", "the_question/game/tl/french/options.rpyc", "the_question/game/tl/french/screens.rpy", "the_question/game/tl/french/screens.rpyc", "the_question/game/tl/french/script.rpy", "the_question/game/tl/french/script.rpyc", "the_question/game/tl/italian/common.rpy", "the_question/game/tl/italian/common.rpyc", "the_question/game/tl/italian/options.rpy", "the_question/game/tl/italian/options.rpyc", "the_question/game/tl/italian/screens.rpy", "the_question/game/tl/italian/screens.rpyc", "the_question/game/tl/italian/script.rpy", "the_question/game/tl/italian/script.rpyc", "the_question/game/tl/japanese/common.rpy", "the_question/game/tl/japanese/common.rpyc", "the_question/game/tl/japanese/options.rpy", "the_question/game/tl/japanese/options.rpyc", "the_question/game/tl/japanese/screens.rpy", "the_question/game/tl/japanese/screens.rpyc", "the_question/game/tl/japanese/script.rpy", "the_question/game/tl/japanese/script.rpyc", "the_question/game/tl/korean/common.rpy", "the_question/game/tl/korean/common.rpyc", "the_question/game/tl/korean/options.rpy", "the_question/game/tl/korean/options.rpyc", "the_question/game/tl/korean/screens.rpy", "the_question/game/tl/korean/screens.rpyc", "the_question/game/tl/korean/script.rpy", "the_question/game/tl/korean/script.rpyc", "the_question/game/tl/malay/common.rpy", "the_question/game/tl/malay/common.rpyc", "the_question/game/tl/malay/options.rpy", "the_question/game/tl/malay/options.rpyc", "the_question/game/tl/malay/screens.rpy", "the_question/game/tl/malay/screens.rpyc", "the_question/game/tl/malay/script.rpy", "the_question/game/tl/malay/script.rpyc", "the_question/game/tl/russian/common.rpy", "the_question/game/tl/russian/common.rpyc", "the_question/game/tl/russian/options.rpy", "the_question/game/tl/russian/options.rpyc", "the_question/game/tl/russian/screens.rpy", "the_question/game/tl/russian/screens.rpyc", "the_question/game/tl/russian/script.rpy", "the_question/game/tl/russian/script.rpyc", "the_question/game/tl/schinese/common.rpy", "the_question/game/tl/schinese/common.rpyc", "the_question/game/tl/schinese/options.rpy", "the_question/game/tl/schinese/options.rpyc", "the_question/game/tl/schinese/screens.rpy", "the_question/game/tl/schinese/screens.rpyc", "the_question/game/tl/schinese/script.rpy", "the_question/game/tl/schinese/script.rpyc", "the_question/game/tl/spanish/common.rpy", "the_question/game/tl/spanish/common.rpyc", "the_question/game/tl/spanish/options.rpy", "the_question/game/tl/spanish/options.rpyc", "the_question/game/tl/spanish/screens.rpy", "the_question/game/tl/spanish/screens.rpyc", "the_question/game/tl/spanish/script.rpy", "the_question/game/tl/spanish/script.rpyc", "the_question/game/tl/tchinese/options.rpy", "the_question/game/tl/tchinese/options.rpyc", "the_question/game/tl/tchinese/screens.rpy", "the_question/game/tl/tchinese/screens.rpyc", "the_question/game/tl/tchinese/script.rpy", "the_question/game/tl/tchinese/script.rpyc", "the_question/game/tl/ukrainian/common.rpy", "the_question/game/tl/ukrainian/common.rpyc", "the_question/game/tl/ukrainian/options.rpy", "the_question/game/tl/ukrainian/options.rpyc", "the_question/game/tl/ukrainian/screens.rpy", "the_question/game/tl/ukrainian/screens.rpyc", "the_question/game/tl/ukrainian/script.rpy", "the_question/game/tl/ukrainian/script.rpyc", "the_question/icon.icns", "the_question/icon.ico", "the_question/ios-icon.png", "the_question/ios-launchimage.png", "the_question/progressive_download.txt", "the_question/project.json", "tutorial/game/01director_support.rpy", "tutorial/game/01director_support.rpyc", "tutorial/game/01example.rpy", "tutorial/game/01example.rpyc", "tutorial/game/cache/bytecode-312.rpyb", "tutorial/game/cache/im-cb24e3330cae70a19aa3d56cd2b0b5c7-4615fefc.png", "tutorial/game/cache/py3analysis.rpyb", "tutorial/game/cache/pyanalysis.rpyb", "tutorial/game/cache/screens.rpyb", "tutorial/game/cache/shaders.txt", "tutorial/game/examples.rpy", "tutorial/game/examples.rpyc", "tutorial/game/exclamation.png", "tutorial/game/gui.rpy", "tutorial/game/gui.rpyc", "tutorial/game/gui/bar/bottom.png", "tutorial/game/gui/bar/left.png", "tutorial/game/gui/bar/right.png", "tutorial/game/gui/bar/top.png", "tutorial/game/gui/button/check_foreground.png", "tutorial/game/gui/button/check_selected_foreground.png", "tutorial/game/gui/button/choice_hover_background.png", "tutorial/game/gui/button/choice_idle_background.png", "tutorial/game/gui/button/hover_background.png", "tutorial/game/gui/button/idle_background.png", "tutorial/game/gui/button/quick_hover_background.png", "tutorial/game/gui/button/quick_idle_background.png", "tutorial/game/gui/button/radio_foreground.png", "tutorial/game/gui/button/radio_selected_foreground.png", "tutorial/game/gui/button/slot_hover_background.png", "tutorial/game/gui/button/slot_idle_background.png", "tutorial/game/gui/frame.png", "tutorial/game/gui/main_menu.jpg", "tutorial/game/gui/mouse0.png", "tutorial/game/gui/mouse1.png", "tutorial/game/gui/mouse2.png", "tutorial/game/gui/namebox.png", "tutorial/game/gui/notify.png", "tutorial/game/gui/nvl.png", "tutorial/game/gui/overlay/confirm.png", "tutorial/game/gui/overlay/game_menu.png", "tutorial/game/gui/overlay/main_menu.png", "tutorial/game/gui/phone/nvl.png", "tutorial/game/gui/phone/overlay/game_menu.png", "tutorial/game/gui/phone/overlay/main_menu.png", "tutorial/game/gui/phone/textbox.png", "tutorial/game/gui/scrollbar/horizontal_hover_bar.png", "tutorial/game/gui/scrollbar/horizontal_hover_thumb.png", "tutorial/game/gui/scrollbar/horizontal_idle_bar.png", "tutorial/game/gui/scrollbar/horizontal_idle_thumb.png", "tutorial/game/gui/scrollbar/vertical_hover_bar.png", "tutorial/game/gui/scrollbar/vertical_hover_thumb.png", "tutorial/game/gui/scrollbar/vertical_idle_bar.png", "tutorial/game/gui/scrollbar/vertical_idle_thumb.png", "tutorial/game/gui/skip.png", "tutorial/game/gui/slider/horizontal_hover_bar.png", "tutorial/game/gui/slider/horizontal_hover_thumb.png", "tutorial/game/gui/slider/horizontal_idle_bar.png", "tutorial/game/gui/slider/horizontal_idle_thumb.png", "tutorial/game/gui/slider/vertical_hover_bar.png", "tutorial/game/gui/slider/vertical_hover_thumb.png", "tutorial/game/gui/slider/vertical_idle_bar.png", "tutorial/game/gui/slider/vertical_idle_thumb.png", "tutorial/game/gui/startextbox.png", "tutorial/game/gui/textbox.png", "tutorial/game/gui/window_icon.png", "tutorial/game/images/bar empty hover.png", "tutorial/game/images/bar empty idle.png", "tutorial/game/images/bar full hover.png", "tutorial/game/images/bar full idle.png", "tutorial/game/images/bar thumb hover.png", "tutorial/game/images/bar thumb idle.png", "tutorial/game/images/bg cave.jpg", "tutorial/game/images/bg panorama.webp", "tutorial/game/images/bg pong field.png", "tutorial/game/images/bg washington.jpg", "tutorial/game/images/bg whitehouse.jpg", "tutorial/game/images/button glossy hover.png", "tutorial/game/images/button glossy idle.png", "tutorial/game/images/check_foreground.png", "tutorial/game/images/check_selected_foreground.png", "tutorial/game/images/concert1.png", "tutorial/game/images/concert2.png", "tutorial/game/images/concert3.png", "tutorial/game/images/eileen concerned.png", "tutorial/game/images/eileen happy.png", "tutorial/game/images/eileen vhappy.png", "tutorial/game/images/hover_background.png", "tutorial/game/images/idle_background.png", "tutorial/game/images/imagedissolve circleiris.png", "tutorial/game/images/imagedissolve circlewipe.png", "tutorial/game/images/imagedissolve dream.png", "tutorial/game/images/imagedissolve teleport.png", "tutorial/game/images/imagemap ground.png", "tutorial/game/images/imagemap hover.png", "tutorial/game/images/imagemap volume hover.png", "tutorial/game/images/imagemap volume idle.png", "tutorial/game/images/imagemap volume insensitive.png", "tutorial/game/images/imagemap volume selected_hover.png", "tutorial/game/images/imagemap volume selected_idle.png", "tutorial/game/images/launcher distribute.png", "tutorial/game/images/launcher step1.webp", "tutorial/game/images/launcher step2.webp", "tutorial/game/images/launcher step3.webp", "tutorial/game/images/launcher step4.webp", "tutorial/game/images/launcher step5.webp", "tutorial/game/images/launcher translate.png", "tutorial/game/images/logo base.png", "tutorial/game/images/logo bw.png", "tutorial/game/images/logo solid.png", "tutorial/game/images/lucy happy.png", "tutorial/game/images/lucy mad.png", "tutorial/game/images/magic.png", "tutorial/game/images/ninepatch paper.png", "tutorial/game/images/ninepatch.png", "tutorial/game/images/popup hrpprefs.png", "tutorial/game/images/popup prefs.png", "tutorial/game/images/popup save.png", "tutorial/game/images/spotlight.png", "tutorial/game/indepth_character.rpy", "tutorial/game/indepth_character.rpyc", "tutorial/game/indepth_displayables.rpy", "tutorial/game/indepth_displayables.rpyc", "tutorial/game/indepth_minigame.rpy", "tutorial/game/indepth_minigame.rpyc", "tutorial/game/indepth_style.rpy", "tutorial/game/indepth_style.rpyc", "tutorial/game/indepth_text.rpy", "tutorial/game/indepth_text.rpyc", "tutorial/game/indepth_transitions.rpy", "tutorial/game/indepth_transitions.rpyc", "tutorial/game/indepth_translations.rpy", "tutorial/game/indepth_translations.rpyc", "tutorial/game/keywords.py", "tutorial/game/oa4_launch.webm", "tutorial/game/options.rpy", "tutorial/game/options.rpyc", "tutorial/game/pong_beep.opus", "tutorial/game/pong_boop.opus", "tutorial/game/punch.opus", "tutorial/game/renpyallstars.ogg", "tutorial/game/screens.rpy", "tutorial/game/screens.rpyc", "tutorial/game/script.rpy", "tutorial/game/script.rpyc", "tutorial/game/sunflower-slow-drag.ogg", "tutorial/game/target1.png", "tutorial/game/target2.png", "tutorial/game/testcases.rpy", "tutorial/game/testcases.rpyc", "tutorial/game/tl/None/common.rpym", "tutorial/game/tl/None/common.rpymc", "tutorial/game/tl/french/01example.rpy", "tutorial/game/tl/french/01example.rpyc", "tutorial/game/tl/french/common.rpy", "tutorial/game/tl/french/common.rpyc", "tutorial/game/tl/french/indepth_character.rpy", "tutorial/game/tl/french/indepth_character.rpyc", "tutorial/game/tl/french/indepth_displayables.rpy", "tutorial/game/tl/french/indepth_displayables.rpyc", "tutorial/game/tl/french/indepth_minigame.rpy", "tutorial/game/tl/french/indepth_minigame.rpyc", "tutorial/game/tl/french/indepth_style.rpy", "tutorial/game/tl/french/indepth_style.rpyc", "tutorial/game/tl/french/indepth_text.rpy", "tutorial/game/tl/french/indepth_text.rpyc", "tutorial/game/tl/french/indepth_transitions.rpy", "tutorial/game/tl/french/indepth_transitions.rpyc", "tutorial/game/tl/french/indepth_translations.rpy", "tutorial/game/tl/french/indepth_translations.rpyc", "tutorial/game/tl/french/options.rpy", "tutorial/game/tl/french/options.rpyc", "tutorial/game/tl/french/screens.rpy", "tutorial/game/tl/french/screens.rpyc", "tutorial/game/tl/french/script.rpy", "tutorial/game/tl/french/script.rpyc", "tutorial/game/tl/french/tutorial_atl.rpy", "tutorial/game/tl/french/tutorial_atl.rpyc", "tutorial/game/tl/french/tutorial_director.rpy", "tutorial/game/tl/french/tutorial_director.rpyc", "tutorial/game/tl/french/tutorial_distribute.rpy", "tutorial/game/tl/french/tutorial_distribute.rpyc", "tutorial/game/tl/french/tutorial_nvlmode.rpy", "tutorial/game/tl/french/tutorial_nvlmode.rpyc", "tutorial/game/tl/french/tutorial_playing.rpy", "tutorial/game/tl/french/tutorial_playing.rpyc", "tutorial/game/tl/french/tutorial_quickstart.rpy", "tutorial/game/tl/french/tutorial_quickstart.rpyc", "tutorial/game/tl/french/tutorial_screen_displayables.rpy", "tutorial/game/tl/french/tutorial_screen_displayables.rpyc", "tutorial/game/tl/french/tutorial_screens.rpy", "tutorial/game/tl/french/tutorial_screens.rpyc", "tutorial/game/tl/french/tutorial_video.rpy", "tutorial/game/tl/french/tutorial_video.rpyc", "tutorial/game/tl/japanese/01example.rpy", "tutorial/game/tl/japanese/01example.rpyc", "tutorial/game/tl/japanese/common.rpy", "tutorial/game/tl/japanese/common.rpyc", "tutorial/game/tl/japanese/indepth_character.rpy", "tutorial/game/tl/japanese/indepth_character.rpyc", "tutorial/game/tl/japanese/indepth_displayables.rpy", "tutorial/game/tl/japanese/indepth_displayables.rpyc", "tutorial/game/tl/japanese/indepth_minigame.rpy", "tutorial/game/tl/japanese/indepth_minigame.rpyc", "tutorial/game/tl/japanese/indepth_style.rpy", "tutorial/game/tl/japanese/indepth_style.rpyc", "tutorial/game/tl/japanese/indepth_text.rpy", "tutorial/game/tl/japanese/indepth_text.rpyc", "tutorial/game/tl/japanese/indepth_transitions.rpy", "tutorial/game/tl/japanese/indepth_transitions.rpyc", "tutorial/game/tl/japanese/indepth_translations.rpy", "tutorial/game/tl/japanese/indepth_translations.rpyc", "tutorial/game/tl/japanese/options.rpy", "tutorial/game/tl/japanese/options.rpyc", "tutorial/game/tl/japanese/screens.rpy", "tutorial/game/tl/japanese/screens.rpyc", "tutorial/game/tl/japanese/script.rpy", "tutorial/game/tl/japanese/script.rpyc", "tutorial/game/tl/japanese/style.rpy", "tutorial/game/tl/japanese/style.rpyc", "tutorial/game/tl/japanese/tutorial_atl.rpy", "tutorial/game/tl/japanese/tutorial_atl.rpyc", "tutorial/game/tl/japanese/tutorial_director.rpy", "tutorial/game/tl/japanese/tutorial_director.rpyc", "tutorial/game/tl/japanese/tutorial_distribute.rpy", "tutorial/game/tl/japanese/tutorial_distribute.rpyc", "tutorial/game/tl/japanese/tutorial_nvlmode.rpy", "tutorial/game/tl/japanese/tutorial_nvlmode.rpyc", "tutorial/game/tl/japanese/tutorial_playing.rpy", "tutorial/game/tl/japanese/tutorial_playing.rpyc", "tutorial/game/tl/japanese/tutorial_quickstart.rpy", "tutorial/game/tl/japanese/tutorial_quickstart.rpyc", "tutorial/game/tl/japanese/tutorial_screen_displayables.rpy", "tutorial/game/tl/japanese/tutorial_screen_displayables.rpyc", "tutorial/game/tl/japanese/tutorial_screens.rpy", "tutorial/game/tl/japanese/tutorial_screens.rpyc", "tutorial/game/tl/japanese/tutorial_video.rpy", "tutorial/game/tl/japanese/tutorial_video.rpyc", "tutorial/game/tl/korean/01example.rpy", "tutorial/game/tl/korean/01example.rpyc", "tutorial/game/tl/korean/common.rpy", "tutorial/game/tl/korean/common.rpyc", "tutorial/game/tl/korean/indepth_character.rpy", "tutorial/game/tl/korean/indepth_character.rpyc", "tutorial/game/tl/korean/indepth_displayables.rpy", "tutorial/game/tl/korean/indepth_displayables.rpyc", "tutorial/game/tl/korean/indepth_minigame.rpy", "tutorial/game/tl/korean/indepth_minigame.rpyc", "tutorial/game/tl/korean/indepth_style.rpy", "tutorial/game/tl/korean/indepth_style.rpyc", "tutorial/game/tl/korean/indepth_text.rpy", "tutorial/game/tl/korean/indepth_text.rpyc", "tutorial/game/tl/korean/indepth_transitions.rpy", "tutorial/game/tl/korean/indepth_transitions.rpyc", "tutorial/game/tl/korean/indepth_translations.rpy", "tutorial/game/tl/korean/indepth_translations.rpyc", "tutorial/game/tl/korean/options.rpy", "tutorial/game/tl/korean/options.rpyc", "tutorial/game/tl/korean/screens.rpy", "tutorial/game/tl/korean/screens.rpyc", "tutorial/game/tl/korean/script.rpy", "tutorial/game/tl/korean/script.rpyc", "tutorial/game/tl/korean/tutorial_atl.rpy", "tutorial/game/tl/korean/tutorial_atl.rpyc", "tutorial/game/tl/korean/tutorial_director.rpy", "tutorial/game/tl/korean/tutorial_director.rpyc", "tutorial/game/tl/korean/tutorial_distribute.rpy", "tutorial/game/tl/korean/tutorial_distribute.rpyc", "tutorial/game/tl/korean/tutorial_nvlmode.rpy", "tutorial/game/tl/korean/tutorial_nvlmode.rpyc", "tutorial/game/tl/korean/tutorial_playing.rpy", "tutorial/game/tl/korean/tutorial_playing.rpyc", "tutorial/game/tl/korean/tutorial_quickstart.rpy", "tutorial/game/tl/korean/tutorial_quickstart.rpyc", "tutorial/game/tl/korean/tutorial_screen_displayables.rpy", "tutorial/game/tl/korean/tutorial_screen_displayables.rpyc", "tutorial/game/tl/korean/tutorial_screens.rpy", "tutorial/game/tl/korean/tutorial_screens.rpyc", "tutorial/game/tl/korean/tutorial_video.rpy", "tutorial/game/tl/korean/tutorial_video.rpyc", "tutorial/game/tl/piglatin/01example.rpy", "tutorial/game/tl/piglatin/01example.rpyc", "tutorial/game/tl/piglatin/common.rpy", "tutorial/game/tl/piglatin/common.rpyc", "tutorial/game/tl/piglatin/indepth_character.rpy", "tutorial/game/tl/piglatin/indepth_character.rpyc", "tutorial/game/tl/piglatin/indepth_displayables.rpy", "tutorial/game/tl/piglatin/indepth_displayables.rpyc", "tutorial/game/tl/piglatin/indepth_minigame.rpy", "tutorial/game/tl/piglatin/indepth_minigame.rpyc", "tutorial/game/tl/piglatin/indepth_style.rpy", "tutorial/game/tl/piglatin/indepth_style.rpyc", "tutorial/game/tl/piglatin/indepth_text.rpy", "tutorial/game/tl/piglatin/indepth_text.rpyc", "tutorial/game/tl/piglatin/indepth_transitions.rpy", "tutorial/game/tl/piglatin/indepth_transitions.rpyc", "tutorial/game/tl/piglatin/indepth_translations.rpy", "tutorial/game/tl/piglatin/indepth_translations.rpyc", "tutorial/game/tl/piglatin/options.rpy", "tutorial/game/tl/piglatin/options.rpyc", "tutorial/game/tl/piglatin/screens.rpy", "tutorial/game/tl/piglatin/screens.rpyc", "tutorial/game/tl/piglatin/script.rpy", "tutorial/game/tl/piglatin/script.rpyc", "tutorial/game/tl/piglatin/tutorial_atl.rpy", "tutorial/game/tl/piglatin/tutorial_atl.rpyc", "tutorial/game/tl/piglatin/tutorial_director.rpy", "tutorial/game/tl/piglatin/tutorial_director.rpyc", "tutorial/game/tl/piglatin/tutorial_distribute.rpy", "tutorial/game/tl/piglatin/tutorial_distribute.rpyc", "tutorial/game/tl/piglatin/tutorial_nvlmode.rpy", "tutorial/game/tl/piglatin/tutorial_nvlmode.rpyc", "tutorial/game/tl/piglatin/tutorial_playing.rpy", "tutorial/game/tl/piglatin/tutorial_playing.rpyc", "tutorial/game/tl/piglatin/tutorial_quickstart.rpy", "tutorial/game/tl/piglatin/tutorial_quickstart.rpyc", "tutorial/game/tl/piglatin/tutorial_screen_displayables.rpy", "tutorial/game/tl/piglatin/tutorial_screen_displayables.rpyc", "tutorial/game/tl/piglatin/tutorial_screens.rpy", "tutorial/game/tl/piglatin/tutorial_screens.rpyc", "tutorial/game/tl/piglatin/tutorial_video.rpy", "tutorial/game/tl/piglatin/tutorial_video.rpyc", "tutorial/game/tl/russian/01example.rpy", "tutorial/game/tl/russian/01example.rpyc", "tutorial/game/tl/russian/common.rpy", "tutorial/game/tl/russian/common.rpyc", "tutorial/game/tl/russian/indepth_character.rpy", "tutorial/game/tl/russian/indepth_character.rpyc", "tutorial/game/tl/russian/indepth_displayables.rpy", "tutorial/game/tl/russian/indepth_displayables.rpyc", "tutorial/game/tl/russian/indepth_minigame.rpy", "tutorial/game/tl/russian/indepth_minigame.rpyc", "tutorial/game/tl/russian/indepth_style.rpy", "tutorial/game/tl/russian/indepth_style.rpyc", "tutorial/game/tl/russian/indepth_text.rpy", "tutorial/game/tl/russian/indepth_text.rpyc", "tutorial/game/tl/russian/indepth_transitions.rpy", "tutorial/game/tl/russian/indepth_transitions.rpyc", "tutorial/game/tl/russian/indepth_translations.rpy", "tutorial/game/tl/russian/indepth_translations.rpyc", "tutorial/game/tl/russian/options.rpy", "tutorial/game/tl/russian/options.rpyc", "tutorial/game/tl/russian/screens.rpy", "tutorial/game/tl/russian/screens.rpyc", "tutorial/game/tl/russian/script.rpy", "tutorial/game/tl/russian/script.rpyc", "tutorial/game/tl/russian/tutorial_atl.rpy", "tutorial/game/tl/russian/tutorial_atl.rpyc", "tutorial/game/tl/russian/tutorial_director.rpy", "tutorial/game/tl/russian/tutorial_director.rpyc", "tutorial/game/tl/russian/tutorial_distribute.rpy", "tutorial/game/tl/russian/tutorial_distribute.rpyc", "tutorial/game/tl/russian/tutorial_nvlmode.rpy", "tutorial/game/tl/russian/tutorial_nvlmode.rpyc", "tutorial/game/tl/russian/tutorial_playing.rpy", "tutorial/game/tl/russian/tutorial_playing.rpyc", "tutorial/game/tl/russian/tutorial_quickstart.rpy", "tutorial/game/tl/russian/tutorial_quickstart.rpyc", "tutorial/game/tl/russian/tutorial_screen_displayables.rpy", "tutorial/game/tl/russian/tutorial_screen_displayables.rpyc", "tutorial/game/tl/russian/tutorial_screens.rpy", "tutorial/game/tl/russian/tutorial_screens.rpyc", "tutorial/game/tl/russian/tutorial_video.rpy", "tutorial/game/tl/russian/tutorial_video.rpyc", "tutorial/game/tl/schinese/01example.rpy", "tutorial/game/tl/schinese/01example.rpyc", "tutorial/game/tl/schinese/common.rpy", "tutorial/game/tl/schinese/common.rpyc", "tutorial/game/tl/schinese/indepth_character.rpy", "tutorial/game/tl/schinese/indepth_character.rpyc", "tutorial/game/tl/schinese/indepth_displayables.rpy", "tutorial/game/tl/schinese/indepth_displayables.rpyc", "tutorial/game/tl/schinese/indepth_minigame.rpy", "tutorial/game/tl/schinese/indepth_minigame.rpyc", "tutorial/game/tl/schinese/indepth_style.rpy", "tutorial/game/tl/schinese/indepth_style.rpyc", "tutorial/game/tl/schinese/indepth_text.rpy", "tutorial/game/tl/schinese/indepth_text.rpyc", "tutorial/game/tl/schinese/indepth_transitions.rpy", "tutorial/game/tl/schinese/indepth_transitions.rpyc", "tutorial/game/tl/schinese/indepth_translations.rpy", "tutorial/game/tl/schinese/indepth_translations.rpyc", "tutorial/game/tl/schinese/options.rpy", "tutorial/game/tl/schinese/options.rpyc", "tutorial/game/tl/schinese/screens.rpy", "tutorial/game/tl/schinese/screens.rpyc", "tutorial/game/tl/schinese/script.rpy", "tutorial/game/tl/schinese/script.rpyc", "tutorial/game/tl/schinese/style.rpy", "tutorial/game/tl/schinese/style.rpyc", "tutorial/game/tl/schinese/tutorial_atl.rpy", "tutorial/game/tl/schinese/tutorial_atl.rpyc", "tutorial/game/tl/schinese/tutorial_director.rpy", "tutorial/game/tl/schinese/tutorial_director.rpyc", "tutorial/game/tl/schinese/tutorial_distribute.rpy", "tutorial/game/tl/schinese/tutorial_distribute.rpyc", "tutorial/game/tl/schinese/tutorial_nvlmode.rpy", "tutorial/game/tl/schinese/tutorial_nvlmode.rpyc", "tutorial/game/tl/schinese/tutorial_playing.rpy", "tutorial/game/tl/schinese/tutorial_playing.rpyc", "tutorial/game/tl/schinese/tutorial_quickstart.rpy", "tutorial/game/tl/schinese/tutorial_quickstart.rpyc", "tutorial/game/tl/schinese/tutorial_screen_displayables.rpy", "tutorial/game/tl/schinese/tutorial_screen_displayables.rpyc", "tutorial/game/tl/schinese/tutorial_screens.rpy", "tutorial/game/tl/schinese/tutorial_screens.rpyc", "tutorial/game/tl/schinese/tutorial_video.rpy", "tutorial/game/tl/schinese/tutorial_video.rpyc", "tutorial/game/tl/spanish/01example.rpy", "tutorial/game/tl/spanish/01example.rpyc", "tutorial/game/tl/spanish/common.rpy", "tutorial/game/tl/spanish/common.rpyc", "tutorial/game/tl/spanish/indepth_character.rpy", "tutorial/game/tl/spanish/indepth_character.rpyc", "tutorial/game/tl/spanish/indepth_displayables.rpy", "tutorial/game/tl/spanish/indepth_displayables.rpyc", "tutorial/game/tl/spanish/indepth_minigame.rpy", "tutorial/game/tl/spanish/indepth_minigame.rpyc", "tutorial/game/tl/spanish/indepth_style.rpy", "tutorial/game/tl/spanish/indepth_style.rpyc", "tutorial/game/tl/spanish/indepth_text.rpy", "tutorial/game/tl/spanish/indepth_text.rpyc", "tutorial/game/tl/spanish/indepth_transitions.rpy", "tutorial/game/tl/spanish/indepth_transitions.rpyc", "tutorial/game/tl/spanish/indepth_translations.rpy", "tutorial/game/tl/spanish/indepth_translations.rpyc", "tutorial/game/tl/spanish/options.rpy", "tutorial/game/tl/spanish/options.rpyc", "tutorial/game/tl/spanish/screens.rpy", "tutorial/game/tl/spanish/screens.rpyc", "tutorial/game/tl/spanish/script.rpy", "tutorial/game/tl/spanish/script.rpyc", "tutorial/game/tl/spanish/tutorial_atl.rpy", "tutorial/game/tl/spanish/tutorial_atl.rpyc", "tutorial/game/tl/spanish/tutorial_director.rpy", "tutorial/game/tl/spanish/tutorial_director.rpyc", "tutorial/game/tl/spanish/tutorial_distribute.rpy", "tutorial/game/tl/spanish/tutorial_distribute.rpyc", "tutorial/game/tl/spanish/tutorial_nvlmode.rpy", "tutorial/game/tl/spanish/tutorial_nvlmode.rpyc", "tutorial/game/tl/spanish/tutorial_playing.rpy", "tutorial/game/tl/spanish/tutorial_playing.rpyc", "tutorial/game/tl/spanish/tutorial_quickstart.rpy", "tutorial/game/tl/spanish/tutorial_quickstart.rpyc", "tutorial/game/tl/spanish/tutorial_screen_displayables.rpy", "tutorial/game/tl/spanish/tutorial_screen_displayables.rpyc", "tutorial/game/tl/spanish/tutorial_screens.rpy", "tutorial/game/tl/spanish/tutorial_screens.rpyc", "tutorial/game/tl/spanish/tutorial_video.rpy", "tutorial/game/tl/spanish/tutorial_video.rpyc", "tutorial/game/tl/ukrainian/01example.rpy", "tutorial/game/tl/ukrainian/01example.rpyc", "tutorial/game/tl/ukrainian/common.rpy", "tutorial/game/tl/ukrainian/common.rpyc", "tutorial/game/tl/ukrainian/indepth_character.rpy", "tutorial/game/tl/ukrainian/indepth_character.rpyc", "tutorial/game/tl/ukrainian/indepth_displayables.rpy", "tutorial/game/tl/ukrainian/indepth_displayables.rpyc", "tutorial/game/tl/ukrainian/indepth_minigame.rpy", "tutorial/game/tl/ukrainian/indepth_minigame.rpyc", "tutorial/game/tl/ukrainian/indepth_style.rpy", "tutorial/game/tl/ukrainian/indepth_style.rpyc", "tutorial/game/tl/ukrainian/indepth_text.rpy", "tutorial/game/tl/ukrainian/indepth_text.rpyc", "tutorial/game/tl/ukrainian/indepth_transitions.rpy", "tutorial/game/tl/ukrainian/indepth_transitions.rpyc", "tutorial/game/tl/ukrainian/indepth_translations.rpy", "tutorial/game/tl/ukrainian/indepth_translations.rpyc", "tutorial/game/tl/ukrainian/options.rpy", "tutorial/game/tl/ukrainian/options.rpyc", "tutorial/game/tl/ukrainian/screens.rpy", "tutorial/game/tl/ukrainian/screens.rpyc", "tutorial/game/tl/ukrainian/script.rpy", "tutorial/game/tl/ukrainian/script.rpyc", "tutorial/game/tl/ukrainian/tutorial_atl.rpy", "tutorial/game/tl/ukrainian/tutorial_atl.rpyc", "tutorial/game/tl/ukrainian/tutorial_director.rpy", "tutorial/game/tl/ukrainian/tutorial_director.rpyc", "tutorial/game/tl/ukrainian/tutorial_distribute.rpy", "tutorial/game/tl/ukrainian/tutorial_distribute.rpyc", "tutorial/game/tl/ukrainian/tutorial_nvlmode.rpy", "tutorial/game/tl/ukrainian/tutorial_nvlmode.rpyc", "tutorial/game/tl/ukrainian/tutorial_playing.rpy", "tutorial/game/tl/ukrainian/tutorial_playing.rpyc", "tutorial/game/tl/ukrainian/tutorial_quickstart.rpy", "tutorial/game/tl/ukrainian/tutorial_quickstart.rpyc", "tutorial/game/tl/ukrainian/tutorial_screen_displayables.rpy", "tutorial/game/tl/ukrainian/tutorial_screen_displayables.rpyc", "tutorial/game/tl/ukrainian/tutorial_screens.rpy", "tutorial/game/tl/ukrainian/tutorial_screens.rpyc", "tutorial/game/tl/ukrainian/tutorial_video.rpy", "tutorial/game/tl/ukrainian/tutorial_video.rpyc", "tutorial/game/tower_clock.ogg", "tutorial/game/tutorial_atl.rpy", "tutorial/game/tutorial_atl.rpyc", "tutorial/game/tutorial_director.rpy", "tutorial/game/tutorial_director.rpyc", "tutorial/game/tutorial_director.rpym", "tutorial/game/tutorial_distribute.rpy", "tutorial/game/tutorial_distribute.rpyc", "tutorial/game/tutorial_nvlmode.rpy", "tutorial/game/tutorial_nvlmode.rpyc", "tutorial/game/tutorial_playing.rpy", "tutorial/game/tutorial_playing.rpyc", "tutorial/game/tutorial_quickstart.rpy", "tutorial/game/tutorial_quickstart.rpyc", "tutorial/game/tutorial_screen_displayables.rpy", "tutorial/game/tutorial_screen_displayables.rpyc", "tutorial/game/tutorial_screens.rpy", "tutorial/game/tutorial_screens.rpyc", "tutorial/game/tutorial_video.rpy", "tutorial/game/tutorial_video.rpyc", "tutorial/progressive_download.txt", "tutorial/project.json"], "directories": ["doc", "doc/_images", "doc/_static", "doc/_static/bootstrap-2.3.2", "doc/_static/bootstrap-2.3.2/css", "doc/_static/bootstrap-2.3.2/img", "doc/_static/bootstrap-2.3.2/js", "doc/_static/bootstrap-3.3.6", "doc/_static/bootstrap-3.3.6/css", "doc/_static/bootstrap-3.3.6/fonts", "doc/_static/bootstrap-3.3.6/js", "doc/_static/bootstrap-3.4.1", "doc/_static/bootstrap-3.4.1/css", "doc/_static/bootstrap-3.4.1/fonts", "doc/_static/bootstrap-3.4.1/js", "doc/_static/bootswatch-2.3.2", "doc/_static/bootswatch-2.3.2/amelia", "doc/_static/bootswatch-2.3.2/cerulean", "doc/_static/bootswatch-2.3.2/cosmo", "doc/_static/bootswatch-2.3.2/cyborg", "doc/_static/bootswatch-2.3.2/flatly", "doc/_static/bootswatch-2.3.2/img", "doc/_static/bootswatch-2.3.2/journal", "doc/_static/bootswatch-2.3.2/readable", "doc/_static/bootswatch-2.3.2/simplex", "doc/_static/bootswatch-2.3.2/slate", "doc/_static/bootswatch-2.3.2/spacelab", "doc/_static/bootswatch-2.3.2/spruce", "doc/_static/bootswatch-2.3.2/superhero", "doc/_static/bootswatch-2.3.2/united", "doc/_static/bootswatch-3.3.6", "doc/_static/bootswatch-3.3.6/cerulean", "doc/_static/bootswatch-3.3.6/cosmo", "doc/_static/bootswatch-3.3.6/custom", "doc/_static/bootswatch-3.3.6/cyborg", "doc/_static/bootswatch-3.3.6/darkly", "doc/_static/bootswatch-3.3.6/flatly", "doc/_static/bootswatch-3.3.6/fonts", "doc/_static/bootswatch-3.3.6/journal", "doc/_static/bootswatch-3.3.6/lumen", "doc/_static/bootswatch-3.3.6/paper", "doc/_static/bootswatch-3.3.6/readable", "doc/_static/bootswatch-3.3.6/sandstone", "doc/_static/bootswatch-3.3.6/simplex", "doc/_static/bootswatch-3.3.6/slate", "doc/_static/bootswatch-3.3.6/spacelab", "doc/_static/bootswatch-3.3.6/superhero", "doc/_static/bootswatch-3.3.6/united", "doc/_static/bootswatch-3.3.6/yeti", "doc/_static/bootswatch-3.4.1", "doc/_static/bootswatch-3.4.1/cerulean", "doc/_static/bootswatch-3.4.1/cosmo", "doc/_static/bootswatch-3.4.1/cyborg", "doc/_static/bootswatch-3.4.1/darkly", "doc/_static/bootswatch-3.4.1/flatly", "doc/_static/bootswatch-3.4.1/fonts", "doc/_static/bootswatch-3.4.1/journal", "doc/_static/bootswatch-3.4.1/lumen", "doc/_static/bootswatch-3.4.1/paper", "doc/_static/bootswatch-3.4.1/readable", "doc/_static/bootswatch-3.4.1/sandstone", "doc/_static/bootswatch-3.4.1/simplex", "doc/_static/bootswatch-3.4.1/slate", "doc/_static/bootswatch-3.4.1/spacelab", "doc/_static/bootswatch-3.4.1/superhero", "doc/_static/bootswatch-3.4.1/united", "doc/_static/bootswatch-3.4.1/yeti", "doc/_static/css", "doc/_static/css/fonts", "doc/_static/dark_mode_css", "doc/_static/dark_mode_js", "doc/_static/js", "gui", "gui/game", "gui/game/audio", "gui/game/cache", "gui/game/gui", "gui/game/images", "gui/game/libs", "launcher", "launcher/game", "launcher/game/cache", "launcher/game/fonts", "launcher/game/gui7", "launcher/game/images", "launcher/game/tl", "launcher/game/tl/arabic", "launcher/game/tl/danish", "launcher/game/tl/finnish", "launcher/game/tl/french", "launcher/game/tl/german", "launcher/game/tl/greek", "launcher/game/tl/indonesian", "launcher/game/tl/italian", "launcher/game/tl/japanese", "launcher/game/tl/korean", "launcher/game/tl/malay", "launcher/game/tl/piglatin", "launcher/game/tl/polish", "launcher/game/tl/portuguese", "launcher/game/tl/russian", "launcher/game/tl/schinese", "launcher/game/tl/spanish", "launcher/game/tl/tchinese", "launcher/game/tl/turkish", "launcher/game/tl/ukrainian", "launcher/game/tl/vietnamese", "launcher/skin", "lib", "lib/py3-linux-x86_64", "lib/py3-mac-universal", "lib/py3-windows-x86_64", "lib/python3.12", "lib/python3.12/android", "lib/python3.12/asyncio", "lib/python3.12/certifi", "lib/python3.12/chardet", "lib/python3.12/chardet/cli", "lib/python3.12/chardet/metadata", "lib/python3.12/collections", "lib/python3.12/concurrent", "lib/python3.12/concurrent/futures", "lib/python3.12/ctypes", "lib/python3.12/ctypes/macholib", "lib/python3.12/ecdsa", "lib/python3.12/email", "lib/python3.12/email/mime", "lib/python3.12/encodings", "lib/python3.12/future", "lib/python3.12/future/backports", "lib/python3.12/future/backports/email", "lib/python3.12/future/backports/email/mime", "lib/python3.12/future/backports/html", "lib/python3.12/future/backports/http", "lib/python3.12/future/backports/test", "lib/python3.12/future/backports/urllib", "lib/python3.12/future/backports/xmlrpc", "lib/python3.12/future/builtins", "lib/python3.12/future/moves", "lib/python3.12/future/moves/dbm", "lib/python3.12/future/moves/html", "lib/python3.12/future/moves/http", "lib/python3.12/future/moves/test", "lib/python3.12/future/moves/tkinter", "lib/python3.12/future/moves/urllib", "lib/python3.12/future/moves/xmlrpc", "lib/python3.12/future/standard_library", "lib/python3.12/future/tests", "lib/python3.12/future/types", "lib/python3.12/future/utils", "lib/python3.12/html", "lib/python3.12/http", "lib/python3.12/idna", "lib/python3.12/importlib", "lib/python3.12/importlib/metadata", "lib/python3.12/importlib/resources", "lib/python3.12/jnius", "lib/python3.12/json", "lib/python3.12/lib-dynload", "lib/python3.12/logging", "lib/python3.12/multiprocessing", "lib/python3.12/multiprocessing/dummy", "lib/python3.12/ordlookup", "lib/python3.12/past", "lib/python3.12/past/builtins", "lib/python3.12/past/translation", "lib/python3.12/past/types", "lib/python3.12/past/utils", "lib/python3.12/pyasn1", "lib/python3.12/pyasn1/codec", "lib/python3.12/pyasn1/codec/ber", "lib/python3.12/pyasn1/codec/cer", "lib/python3.12/pyasn1/codec/der", "lib/python3.12/pyasn1/codec/native", "lib/python3.12/pyasn1/compat", "lib/python3.12/pyasn1/type", "lib/python3.12/pygame_sdl2", "lib/python3.12/pygame_sdl2/threads", "lib/python3.12/pyobjus", "lib/python3.12/re", "lib/python3.12/requests", "lib/python3.12/rsa", "lib/python3.12/urllib", "lib/python3.12/urllib3", "lib/python3.12/urllib3/contrib", "lib/python3.12/urllib3/contrib/emscripten", "lib/python3.12/urllib3/util", "lib/python3.12/websockets", "lib/python3.12/websockets/extensions", "lib/python3.12/websockets/legacy", "lib/python3.12/websockets/sync", "lib/python3.12/xml", "lib/python3.12/xml/dom", "lib/python3.12/xml/etree", "lib/python3.12/xml/parsers", "lib/python3.12/xml/sax", "lib/python3.12/zipfile", "lib/python3.12/zipfile/_path", "lib/python3.12/zoneinfo", "renpy", "renpy.app", "renpy.app/Contents", "renpy.app/Contents/MacOS", "renpy.app/Contents/Resources", "renpy.app/Contents/_CodeSignature", "renpy/__pycache__", "renpy/angle", "renpy/audio", "renpy/audio/__pycache__", "renpy/common", "renpy/common/__pycache__", "renpy/common/_compat", "renpy/common/_developer", "renpy/common/_layout", "renpy/common/_outline", "renpy/common/_placeholder", "renpy/common/_roundrect", "renpy/common/_theme_amie2", "renpy/common/_theme_austen", "renpy/common/_theme_awt", "renpy/common/_theme_bordered", "renpy/common/_theme_crayon", "renpy/common/_theme_diamond", "renpy/common/_theme_glow", "renpy/common/_theme_marker", "renpy/common/_theme_regal", "renpy/common/_theme_threeD", "renpy/common/_theme_tv", "renpy/compat", "renpy/compat/__pycache__", "renpy/display", "renpy/display/__pycache__", "renpy/exports", "renpy/exports/__pycache__", "renpy/gl2", "renpy/gl2/__pycache__", "renpy/sl2", "renpy/sl2/__pycache__", "renpy/styledata", "renpy/styledata/__pycache__", "renpy/test", "renpy/test/__pycache__", "renpy/text", "renpy/text/__pycache__", "renpy/translation", "renpy/translation/__pycache__", "renpy/uguu", "renpy/uguu/__pycache__", "renpy/update", "renpy/update/__pycache__", "sdk-fonts", "the_question", "the_question/game", "the_question/game/cache", "the_question/game/gui", "the_question/game/gui/bar", "the_question/game/gui/button", "the_question/game/gui/overlay", "the_question/game/gui/phone", "the_question/game/gui/phone/overlay", "the_question/game/gui/scrollbar", "the_question/game/gui/slider", "the_question/game/images", "the_question/game/tl", "the_question/game/tl/None", "the_question/game/tl/czech", "the_question/game/tl/danish", "the_question/game/tl/french", "the_question/game/tl/italian", "the_question/game/tl/japanese", "the_question/game/tl/korean", "the_question/game/tl/malay", "the_question/game/tl/russian", "the_question/game/tl/schinese", "the_question/game/tl/spanish", "the_question/game/tl/tchinese", "the_question/game/tl/ukrainian", "tutorial", "tutorial/game", "tutorial/game/cache", "tutorial/game/gui", "tutorial/game/gui/bar", "tutorial/game/gui/button", "tutorial/game/gui/overlay", "tutorial/game/gui/phone", "tutorial/game/gui/phone/overlay", "tutorial/game/gui/scrollbar", "tutorial/game/gui/slider", "tutorial/game/images", "tutorial/game/tl", "tutorial/game/tl/None", "tutorial/game/tl/french", "tutorial/game/tl/japanese", "tutorial/game/tl/korean", "tutorial/game/tl/piglatin", "tutorial/game/tl/russian", "tutorial/game/tl/schinese", "tutorial/game/tl/spanish", "tutorial/game/tl/ukrainian"], "xbit": ["lib/py3-linux-x86_64/librenpython.so", "lib/py3-linux-x86_64/python", "lib/py3-linux-x86_64/pythonw", "lib/py3-linux-x86_64/renpy", "lib/py3-linux-x86_64/zsync", "lib/py3-linux-x86_64/zsyncmake", "lib/py3-mac-universal/librenpython.dylib", "lib/py3-mac-universal/python", "lib/py3-mac-universal/pythonw", "lib/py3-mac-universal/renpy", "lib/py3-mac-universal/zsync", "lib/py3-mac-universal/zsyncmake", "renpy.app/Contents/MacOS/librenpython.dylib", "renpy.app/Contents/MacOS/python", "renpy.app/Contents/MacOS/pythonw", "renpy.app/Contents/MacOS/renpy", "renpy.app/Contents/MacOS/zsync", "renpy.app/Contents/MacOS/zsyncmake", "renpy.sh"]}}