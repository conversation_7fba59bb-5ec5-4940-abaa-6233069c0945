﻿
# game/indepth_transitions.rpy:56
translate ukrainian demo_transitions_5bbc72fe:

    # e "Ren'Py ships with a large number of built-in transitions, and also includes classes that let you define your own."
    e "Ren'Py поставляється з великою кількістю вбудованих переходів, а також включає класи, які дозволяють визначати власні."

# game/indepth_transitions.rpy:60
translate ukrainian demo_transitions_menu_3caf78d3:

    # e "What kind of transitions would you like demonstrated?" nointeract
    e "Які переходи ви б хотіли подивитись?" nointeract

# game/indepth_transitions.rpy:95
translate ukrainian demo_simple_transitions_2b4fbae3:

    # e "Okay, I can tell you about simple transitions. We call them simple because they don't take much in the way of configuration."
    e "Гаразд, я можу розповісти вам про прості переходи. Ми називаємо їх простими, тому що вони не вимагають багато налаштувань."

# game/indepth_transitions.rpy:97
translate ukrainian demo_simple_transitions_4b235ac2:

    # e "But don't let that get you down, since they're the transitions you'll probably use the most in a game."
    e "Але нехай це вас не засмучує, оскільки це ті переходи, які ви, ймовірно, найчастіше використовуватимете в грі."

# game/indepth_transitions.rpy:103
translate ukrainian demo_simple_transitions_af0431ac:

    # e "The 'dissolve' transition is probably the most useful, blending one scene into another."
    e "Перехід 'dissolve', мабуть, найкорисніший, змішуючи одну сцену з іншою."

# game/indepth_transitions.rpy:109
translate ukrainian demo_simple_transitions_5b9f711f:

    # e "The 'Dissolve' function lets you create your own dissolves, taking a different amount of time."
    e "Функція 'Dissolve' дозволяє створювати власні розчини, що займають різну кількість часу."

# game/indepth_transitions.rpy:116
translate ukrainian demo_simple_transitions_79816523:

    # e "The 'fade' transition fades to black, and then fades back in to the new scene."
    e "Перехід 'fade' стає чорним, а потім повертається до нової сцени."

# game/indepth_transitions.rpy:118
translate ukrainian demo_simple_transitions_141bb95d:

    # e "If you're going to stay at a black screen, you'll probably want to use 'dissolve' rather than 'fade'."
    e "Якщо ви збираєтеся залишатися на чорному екрані, ви, ймовірно, захочете використовувати 'dissolve', а не 'fade'."

# game/indepth_transitions.rpy:123
translate ukrainian demo_simple_transitions_f059f4ae:

    # e "You can use 'Fade' to define your own fades. By changing the timing and the color faded to, you can use this for special effects, like flashbulbs."
    e "Ви можете використовувати 'Fade' для визначення власних переходів. Змінивши хронометраж і колір, який появляється, ви можете використовувати це для спеціальних ефектів, наприклад спалахів."

# game/indepth_transitions.rpy:129
translate ukrainian demo_simple_transitions_e948905b:

    # e "The 'pixellate' transition pixellates out the old scene, switches to the new scene, and then unpixellates that."
    e "Перехід 'pixellate' пікселізує стару сцену, перемикається на нову сцену, а потім показує її."

# game/indepth_transitions.rpy:131
translate ukrainian demo_simple_transitions_6a1ae05f:

    # e "It's probably not appropriate for most games, but we think it's kind of neat."
    e "Можливо, це не підходить для більшості ігор, але ми вважаємо, що це начебто гарно."

# game/indepth_transitions.rpy:134
translate ukrainian demo_simple_transitions_bdfcd85a:

    # e "You can use 'Pixellate' to change the details of the pixellation."
    e "Ви можете використовувати 'Pixellate', щоб змінити деталі пікселізації."

# game/indepth_transitions.rpy:136
translate ukrainian demo_simple_transitions_432f7224:

    # e "Motions can also be used as transitions."
    e "Рухи також можна використовувати як переходи."

# game/indepth_transitions.rpy:138
translate ukrainian demo_simple_transitions_a20cefa7:

    # "..."
    "..."

# game/indepth_transitions.rpy:140
translate ukrainian demo_simple_transitions_0fd4d656:

    # "......"
    "......"

# game/indepth_transitions.rpy:146
translate ukrainian demo_simple_transitions_fbf11906:

    # e "Hey! Pay attention."
    e "Агов! Звернутіть увагу."

# game/indepth_transitions.rpy:148
translate ukrainian demo_simple_transitions_51c1c5b8:

    # e "I was about to demonstrate 'vpunch'... well, I guess I just did."
    e "Я збиралася продемонструвати 'vpunch'... ну, мабуть, щойно зробила."

# game/indepth_transitions.rpy:154
translate ukrainian demo_simple_transitions_57f19473:

    # e "We can also shake the screen horizontally, with 'hpunch'. These were defined using the 'Move' function."
    e "Ми також можемо струсити екран горизонтально за допомогою 'hpunch'. Вони були визначені за допомогою функції 'Move'."

# game/indepth_transitions.rpy:156
translate ukrainian demo_simple_transitions_fce83e12:

    # e "There's also the 'move' transition, which is confusingly enough defined using the 'MoveTransition' function."
    e "Існує також перехід 'move', який досить заплутано визначається за допомогою функції 'MoveTransition'."

# game/indepth_transitions.rpy:164
translate ukrainian demo_simple_transitions_1050b6a4:

    # e "The 'move' transition finds images that have changed placement, and slides them to their new place. It's an easy way to get motion in your game."
    e "Перехід 'move' знаходить зображення, які змінили розташування, і переміщує їх на нове місце. Це простий спосіб активізувати вашу гру."

# game/indepth_transitions.rpy:168
translate ukrainian demo_simple_transitions_dc776e59:

    # e "That's it for the simple transitions."
    e "Ось і все для простих переходів."

# game/indepth_transitions.rpy:175
translate ukrainian demo_imagedissolve_transitions_2db67018:

    # e "Perhaps the most flexible kind of transition is the ImageDissolve, which lets you use an image to control a dissolve."
    e "Мабуть, найгнучкішим типом переходу є ImageDissolve, який дозволяє використовувати зображення для керування розчиненням."

# game/indepth_transitions.rpy:177
translate ukrainian demo_imagedissolve_transitions_429f8d03:

    # e "This lets us specify very complex transitions, fairly simply. Let's try some, and then I'll show you how they work."
    e "Це дозволяє нам вказати дуже складні переходи, досить просто. Давайте спробуємо, а потім я покажу вам, як вони працюють."

# game/indepth_transitions.rpy:179
translate ukrainian demo_imagedissolve_transitions_1ce501b0:

    # e "There are two ImageDissolve transitions built into Ren'Py."
    e "У Ren'Py вбудовано два переходи ImageDissolve."

# game/indepth_transitions.rpy:191
translate ukrainian demo_imagedissolve_transitions_ea0413be:

    # e "The 'blinds' transition opens and closes what looks like vertical blinds."
    e "Перехід 'blinds' відкриває та закриває те, що виглядає як вертикальні жалюзі."

# game/indepth_transitions.rpy:201
translate ukrainian demo_imagedissolve_transitions_12e2e0d0:

    # e "The 'squares' transition uses these squares to show things."
    e "Перехід 'squares' використовує ці квадрати, щоб показати речі."

# game/indepth_transitions.rpy:203
translate ukrainian demo_imagedissolve_transitions_bbf73d1c:

    # e "I'm not sure why anyone would want to use it, but it was used in some translated games, so we added it."
    e "Я не впевнена, чому хтось хоче використовувати його, але він використовувався в деяких перекладених іграх, тому ми додали його."

# game/indepth_transitions.rpy:207
translate ukrainian demo_imagedissolve_transitions_0ab2902d:

    # e "The most interesting transitions aren't in the standard library."
    e "Найцікавіших переходів немає в стандартній бібліотеці."

# game/indepth_transitions.rpy:209
translate ukrainian demo_imagedissolve_transitions_54aa9bf9:

    # e "These ones require an image the size of the screen, and so we couldn't include them as the size of the screen can change from game to game."
    e "Для них потрібне зображення розміром з екран, тому ми не могли включити їх, оскільки розмір екрана може змінюватися від гри до гри."

# game/indepth_transitions.rpy:215
translate ukrainian demo_imagedissolve_transitions_ca316184:

    # e "We can hide things with a 'circleirisin'..."
    e "Ми можемо приховати речі за допомогою 'circleirisin'..."

# game/indepth_transitions.rpy:221
translate ukrainian demo_imagedissolve_transitions_b8fdf2b6:

    # e "... and show them again with a 'circleirisout'."
    e "... і знову показати їх за допомогою 'circleirisout'."

# game/indepth_transitions.rpy:227
translate ukrainian demo_imagedissolve_transitions_ee427486:

    # e "The 'circlewipe' transitions changes screens using a circular wipe effect."
    e "Переходи 'circlewipe' змінюють екрани за допомогою ефекту кругового витирання."

# game/indepth_transitions.rpy:233
translate ukrainian demo_imagedissolve_transitions_6f089276:

    # e "The 'dream' transition does this weird wavy dissolve, and does it relatively slowly."
    e "Перехід 'dream' робить це дивне хвилеподібне розчинення, і робить це відносно повільно."

# game/indepth_transitions.rpy:239
translate ukrainian demo_imagedissolve_transitions_c0b9d74d:

    # e "The 'teleport' transition reveals the new scene one line at a time."
    e "Перехід 'teleport' відкриває нову сцену один рядок за раз."

# game/indepth_transitions.rpy:246
translate ukrainian demo_imagedissolve_transitions_72ba11d4:

    # e "This is the background picture used with the circleirisout transition."
    e "Це фонове зображення, яке використовується з переходом коло-рисунок."

# game/indepth_transitions.rpy:248
translate ukrainian demo_imagedissolve_transitions_fc3b3339:

    # e "When we use an ImageDissolve, the white will dissolve in first, followed by progressively darker colors. Let's try it."
    e "Коли ми використовуємо ImageDissolve, спочатку розчинятиметься білий колір, а потім поступово з’являться темніші кольори. Давайте спробуємо."

# game/indepth_transitions.rpy:255
translate ukrainian demo_imagedissolve_transitions_4327dca2:

    # e "If we give ImageDissolve the 'reverse' parameter, black areas will dissolve in first."
    e "Якщо ми надамо ImageDissolve параметр 'reverse', чорні області розчиняться спочатку."

# game/indepth_transitions.rpy:260
translate ukrainian demo_imagedissolve_transitions_3a401ee7:

    # e "This lets circleirisin and circleirisout use the same image."
    e "Це дозволяє circleirisin і circleirisout використовувати те саме зображення."

# game/indepth_transitions.rpy:267
translate ukrainian demo_imagedissolve_transitions_20d9cf6c:

    # e "The teleport transition uses a different image, one that dissolves things in one line at a time."
    e "Перехід teleport використовує інше зображення, яке розчиняє речі в одному рядку за раз."

# game/indepth_transitions.rpy:272
translate ukrainian demo_imagedissolve_transitions_906f7800:

    # e "A dissolve only seems to affect parts of the scene that have changed..."
    e "Здається, що dissolve впливає лише на ті частини сцени, які змінилися..."

# game/indepth_transitions.rpy:277
translate ukrainian demo_imagedissolve_transitions_4b557a29:

    # e "... which is how we apply the teleport effect to a single character."
    e "... таким чином ми застосовуємо ефект телепортації до одного персонажа."

# game/indepth_transitions.rpy:285
translate ukrainian demo_cropmove_transitions_1711a91e:

    # e "The CropMove transition class provides a wide range of transition effects. It's not used very much in practice, though."
    e "Клас переходу CropMove надає широкий спектр ефектів переходу. Однак на практиці він не дуже використовується."

# game/indepth_transitions.rpy:290
translate ukrainian demo_cropmove_transitions_6d82cfd7:

    # e "I'll stand offscreen, so you can see some of its modes. I'll read out the mode name after each transition."
    e "Я буду стояти поза кадром, щоб ви могли бачити деякі його режими. Я зачитуватиму назву режиму після кожного переходу."

# game/indepth_transitions.rpy:296
translate ukrainian demo_cropmove_transitions_4427c78c:

    # e "We first have wiperight..."
    e "Ми спочатку маємо wiperight..."

# game/indepth_transitions.rpy:302
translate ukrainian demo_cropmove_transitions_6d1810a1:

    # e "...followed by wipeleft... "
    e "...за ним wipeleft..."

# game/indepth_transitions.rpy:308
translate ukrainian demo_cropmove_transitions_1dd1c6a1:

    # e "...wipeup..."
    e "...wipeup..."

# game/indepth_transitions.rpy:314
translate ukrainian demo_cropmove_transitions_0ea0fa83:

    # e "...and wipedown."
    e "...та wipedown."

# game/indepth_transitions.rpy:316
translate ukrainian demo_cropmove_transitions_c7cb4c16:

    # e "Next, the slides."
    e "Далі слайди."

# game/indepth_transitions.rpy:322
translate ukrainian demo_cropmove_transitions_462a442f:

    # e "Slideright..."
    e "Slideright..."

# game/indepth_transitions.rpy:328
translate ukrainian demo_cropmove_transitions_f9a2e523:

    # e "...slideleft..."
    e "...slideleft..."

# game/indepth_transitions.rpy:334
translate ukrainian demo_cropmove_transitions_20ce3e9c:

    # e "...slideup..."
    e "...slideup..."

# game/indepth_transitions.rpy:340
translate ukrainian demo_cropmove_transitions_9e00a7a6:

    # e "and slidedown."
    e "та slidedown"

# game/indepth_transitions.rpy:342
translate ukrainian demo_cropmove_transitions_b8a710c1:

    # e "While the slide transitions slide in the new scene, the slideaways slide out the old scene."
    e "У той час як переходи slide ковзають у нову сцену, slideaways сковзають зі старої сцени."

# game/indepth_transitions.rpy:349
translate ukrainian demo_cropmove_transitions_1efb4cd0:

    # e "Slideawayright..."
    e "Slideawayright..."

# game/indepth_transitions.rpy:355
translate ukrainian demo_cropmove_transitions_bfb5dfd7:

    # e "...slideawayleft..."
    e "...slideawayleft..."

# game/indepth_transitions.rpy:361
translate ukrainian demo_cropmove_transitions_6c1a4a6f:

    # e "...slideawayup..."
    e "...slideawayup..."

# game/indepth_transitions.rpy:367
translate ukrainian demo_cropmove_transitions_1f994a7b:

    # e "and slideawaydown."
    e "та slideawaydown."

# game/indepth_transitions.rpy:369
translate ukrainian demo_cropmove_transitions_025ef723:

    # e "We also have a couple of transitions that use a rectangular iris."
    e "У нас також є пара переходів, які використовують прямокутну діафрагму."

# game/indepth_transitions.rpy:376
translate ukrainian demo_cropmove_transitions_d00d505e:

    # e "There's irisout..."
    e "Це irisout..."

# game/indepth_transitions.rpy:383
translate ukrainian demo_cropmove_transitions_016a1e0a:

    # e "... and irisin."
    e "... та irisin"

# game/indepth_transitions.rpy:387
translate ukrainian demo_cropmove_transitions_08d753ed:

    # e "It's enough to make you feel a bit dizzy."
    e "Цього достатньо, щоб у вас трохи запаморочилося."

# game/indepth_transitions.rpy:393
translate ukrainian demo_pushmove_transitions_003e506d:

    # e "The PushMove transitions use the new scene to push the old one out. Let's take a look."
    e "Переходи PushMove використовують нову сцену, щоб витіснити стару. Давайте поглянемо."

# game/indepth_transitions.rpy:401
translate ukrainian demo_pushmove_transitions_124f375d:

    # "There's pushright..."
    "Це pushright..."

# game/indepth_transitions.rpy:408
translate ukrainian demo_pushmove_transitions_ce380ccb:

    # "...pushleft..."
    "...pushleft..."

# game/indepth_transitions.rpy:416
translate ukrainian demo_pushmove_transitions_77629638:

    # "...pushdown..."
    "...pushdown..."

# game/indepth_transitions.rpy:424
translate ukrainian demo_pushmove_transitions_b7f33c95:

    # "... and pushup. And that's it the for the PushMove-based transitions."
    "... та pushup. І це все для переходів на основі PushMove."

# game/indepth_transitions.rpy:434
translate ukrainian demo_movetransition_14df0e34:

    # e "The most common MoveTransition is move, which slides around images that have changed position on the screen."
    e "Найпоширенішим MoveTransition є переміщення, яке ковзає навколо зображень, які змінили положення на екрані."

# game/indepth_transitions.rpy:442
translate ukrainian demo_movetransition_84e40422:

    # e "Just like that."
    e "Ось так."

# game/indepth_transitions.rpy:446
translate ukrainian demo_movetransition_098ee9f1:

    # e "There are also the moveout and movein transitions."
    e "Є також переходи moveout та movein."

# game/indepth_transitions.rpy:448
translate ukrainian demo_movetransition_09748f81:

    # e "The moveout transitions (moveoutleft, moveoutright, moveouttop, and moveoutbottom) slide hidden images off the appropriate side of the screen."
    e "Переходи moveout (moveoutleft, moveoutright, moveouttop і moveoutbottom) зсувають приховані зображення з відповідної сторони екрана."

# game/indepth_transitions.rpy:450
translate ukrainian demo_movetransition_5edf6007:

    # e "The movein transitions (moveinleft, moveinright, moveintop, and moveinbottom) slide in new images."
    e "Переходи movein (moveinleft, moveinright, moveintop, і moveinbottom) ковзають у нових зображеннях."

# game/indepth_transitions.rpy:452
translate ukrainian demo_movetransition_20946d36:

    # e "Let's see them all in action."
    e "Давайте подивимося їх усіх у дії."

# game/indepth_transitions.rpy:487
translate ukrainian demo_movetransition_569952e3:

    # e "That's it for the moveins and moveouts."
    e "Ось і все, що стосується movein і moveout."

# game/indepth_transitions.rpy:489
translate ukrainian demo_movetransition_bbb75540:

    # e "Finally, there are the zoomin and zoomout transitions, which show and hide things using a zoom."
    e "На кінець, є переходи zoomin та zoomout, які показують і приховують об’єкти за допомогою масштабування."

# game/indepth_transitions.rpy:499
translate ukrainian demo_movetransition_dc5ccd54:

    # e "And that's all there is."
    e "І це все, що є."

# game/indepth_transitions.rpy:508
translate ukrainian demo_alphadissolve_51613c02:

    # e "The AlphaDissolve transition lets you use one displayable to combine two others. Click, and I'll show you an example."
    e "Перехід AlphaDissolve дозволяє використовувати один відображуваний об’єкт для об’єднання двох інших. Натисніть, і я покажу вам приклад."

# game/indepth_transitions.rpy:518
translate ukrainian demo_alphadissolve_7c08cf8b:

    # e "The AlphaDissolve displayable takes a control displayable, usually an ATL transform."
    e "Відображуваний AlphaDissolve приймає відображуваний елемент керування, як правило, перетворення ATL."

# game/indepth_transitions.rpy:523
translate ukrainian demo_alphadissolve_068e3e98:

    # e "To be useful, the control displayable should be partially transparent."
    e "Щоб бути корисним, відображуваний елемент керування має бути частково прозорим."

# game/indepth_transitions.rpy:525
translate ukrainian demo_alphadissolve_6a1b6203:

    # e "During an AlphaDissolve, the old screen is used to fill the transparent areas of the image, while the new screen fills the opaque areas."
    e "Під час AlphaDissolve старий екран використовується для заповнення прозорих областей зображення, тоді як новий екран заповнює непрозорі області."

# game/indepth_transitions.rpy:529
translate ukrainian demo_alphadissolve_80a728b6:

    # e "For our spotlight example, the old screen is this all-black image."
    e "Для нашого прикладу в центрі уваги старий екран — це повністю чорне зображення."

# game/indepth_transitions.rpy:534
translate ukrainian demo_alphadissolve_ce4380eb:

    # e "The new screen is me just standing here."
    e "На новому екрані я просто стою тут."

# game/indepth_transitions.rpy:542
translate ukrainian demo_alphadissolve_2e95917b:

    # e "By combining them using AlphaDissolve, we can build a complicated effect out of simpler parts."
    e "Комбінуючи їх за допомогою AlphaDissolve, ми можемо створити складний ефект із простіших частин."

translate ukrainian strings:

    # game/indepth_transitions.rpy:58
    old "Simple Transitions"
    new "Прості переходи"

    # game/indepth_transitions.rpy:58
    old "ImageDissolve Transitions"
    new "Переходи ImageDissolve"

    # game/indepth_transitions.rpy:58
    old "MoveTransition Transitions"
    new "Переходи MoveTransition"

    # game/indepth_transitions.rpy:58
    old "CropMove Transitions"
    new "Переходи CropMove"

    # game/indepth_transitions.rpy:58
    old "PushMove Transitions"
    new "Переходи PushMove"

    # game/indepth_transitions.rpy:58
    old "AlphaDissolve Transitions"
    new "Переходи AlphaDissolve"

    # game/indepth_transitions.rpy:58
    old "How about something else?"
    new "Як щодо чогось іншого?"

