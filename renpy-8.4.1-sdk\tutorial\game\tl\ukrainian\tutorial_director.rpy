﻿
# game/tutorial_director.rpy:5
translate ukrainian director_e4543d9b:

    # e "There are a few tools you can access by pressing the right commands on the keyboard."
    e "Є кілька інструментів, до яких можна отримати доступ, натискаючи потрібні команди на клавіатурі."

# game/tutorial_director.rpy:7
translate ukrainian director_ebf40500:

    # e "Typing Shift+R turns on autoreload mode. When it's enabled, your game will automatically reload when you edit a script file."
    e "Введення Shift+R вмикає режим автоперезавантаження. Якщо його ввімкнено, ваша гра автоматично перезавантажуватиметься під час редагування файлу скрипту."

# game/tutorial_director.rpy:9
translate ukrainian director_6f3d1bea:

    # e "Shift+O brings you to the console, which lets you enter Ren'Py and Python commands to try them out."
    e "Shift+O відкриває консоль, де можна вводити команди Ren'Py і Python, щоб протестити їх."

# game/tutorial_director.rpy:11
translate ukrainian director_70a61c1c:

    # e "Shift+D pops up a developer menu with access to these and other functions."
    e "Shift+D відкриває меню розробника з доступом до цих та інших функцій."

# game/tutorial_director.rpy:13
translate ukrainian director_43504744:

    # e "The most powerful tool is the interactive director that lets you add images, music, and voice lines to your game from inside Ren'Py."
    e "Найпотужнішим інструментом є інтерактивний директор, який дозволяє додавати зображення, музику та голосові репліки до вашої гри з Ren'Py."

# game/tutorial_director.rpy:15
translate ukrainian director_32f8695e:

    # e "The idea is that you can use an editor to write the script and logic of your visual novel, and then interactively add images in the right places."
    e "Ідея полягає в тому, що ви можете використовувати редактор, щоб написати скрипт і логіку вашого візуального роману, а потім інтерактивно додавати зображення в потрібних місцях."

# game/tutorial_director.rpy:21
translate ukrainian director_62734181:

    # e "It looks like Ren'Py is installed read-only on your system, so you won't be able to try out the interactive director now."
    e "Схоже, у вашій системі Ren'Py встановлено лише для читання, тому ви не зможете зараз випробувати інтерактивний директор."

# game/tutorial_director.rpy:23
translate ukrainian director_aec4c7d4:

    # e "You'll need to make your own project, and try it out there. But I can tell you how to use it."
    e "Вам потрібно буде створити власний проєкт і спробувати його там. Але я можу розповісти, як ним користуватися."

# game/tutorial_director.rpy:29
translate ukrainian director_453d4d67:

    # e "You can try the interactive director out right now, by using it to change this tutorial game."
    e "Ви можете випробувати інтерактивний директор прямо зараз, використовуючи його, щоб змінити цей навчальний посібник."

# game/tutorial_director.rpy:31
translate ukrainian director_c35284d1:

    # e "Be sure to click my dialogue at the bottom of the screen to advance the tutorial."
    e "Обов’язково клацніть моє діалогове вікно внизу екрана, щоб перейти до навчального посібника."

# game/tutorial_director.rpy:33
translate ukrainian director_e253e03b:

    # e "If something goes wrong, don't worry. Quitting and restarting this tutorial will remove your changes and bring everything back to normal."
    e "Якщо щось піде не так, не хвилюйтеся. Вихід і перезапуск цього навчального посібника видалить ваші зміни та поверне все до нормального стану."

# game/tutorial_director.rpy:42
translate ukrainian director_1cd735bc:

    # e "To get started, let's go back to a blank slate, with no images on the screen."
    e "Щоб почати, давайте повернемося до чистого аркуша, без зображень на екрані."

# game/tutorial_director.rpy:44
translate ukrainian director_c7a18979:

    # e "You can show the director at any time by pressing the 'D' key on your keyboard. Ren'Py will reload, and you'll come back here. Try it now."
    e "Ви можете показати директора в будь-який час, натиснувши клавішу 'D' на клавіатурі. Ren'Py перезавантажиться, і ви повернетеся сюди. Спробуй зараз."

# game/tutorial_director.rpy:46
translate ukrainian director_3dcc4362:

    # e "Let's add a background. Click the '+' to pick where to add it, then the 'scene' statement and 'washington' for the image. Finally, click 'Add' to add it."
    e "Додамо фон. Клацніть '+', щоб вибрати, куди його додати, потім твердження 'scene' і 'washington' для зображення. Нарешті натисніть 'Додати', щоб додати його."

# game/tutorial_director.rpy:48
translate ukrainian director_292d58b5:

    # e "Next, add a sprite. Click '+', then 'show', 'eileen', 'happy', and 'Add'. Once you've added it, dissolve it in by clicking the second '+', then 'with', 'dissolve', and 'Add'."
    e "Далі додайте спрайт. Натисніть '+', потім 'показати', 'eileen', 'happy' і 'Add'. Додавши його, розчиніть його, натиснувши другий '+', потім 'з', 'розчинити' та 'Додати'."

# game/tutorial_director.rpy:52
translate ukrainian director_c875c1a7:

    # e "You can edit or remove statements with the pencil icon. You can move me to the right by editing the show statement, then clicking '(transform)', 'right', and 'Change'."
    e "Ви можете редагувати або видаляти заяви за допомогою піктограми олівця. Ви можете перемістити мене праворуч, відредагувавши оператор show, а потім натиснувши '(transform)', 'right' і 'Change'."

# game/tutorial_director.rpy:54
translate ukrainian director_4e04a74e:

    # e "Finally, you can use the play, queue, stop, and voice statements to manage audio. Try adding 'play', 'music', 'sunflower-slow-drag.ogg'."
    e "Тепер, ви можете використовувати команди відтворення, черги, зупинки та голосові команди для керування звуком. Спробуйте додати 'play', 'music', 'sunflower-slow-drag.ogg'."

# game/tutorial_director.rpy:61
translate ukrainian director_1364b336:

    # l "Finally, I get some more screen time!"
    l "Нарешті я маю більше часу перед екраном!"

# game/tutorial_director.rpy:69
translate ukrainian director_c6dd0c81:

    # e "The changes you make with the director are permanent. They're saved to the script, and you can rollback or repeat this section to see them."
    e "Зміни, які ви вносите разом з директором, є постійними. Вони зберігаються в скрипті, і ви можете відкотити або повторити цей розділ, щоб побачити їх."

# game/tutorial_director.rpy:71
translate ukrainian director_9d03b14b:

    # e "However, we reset this tutorial when the game restarts, so you can try again from a clean slate. That won't happen with your own visual novel."
    e "Однак ми скидаємо цей навчальний посібник, коли гра перезапускається, тож ви можете спробувати знову з чистого аркуша. Цього не станеться з вашим власним візуальним романом."

# game/tutorial_director.rpy:73
translate ukrainian director_dbfa07b2:

    # e "I hope these tools make developing your visual novel that much easier."
    e "Я сподіваюся, що ці інструменти значно полегшать розробку вашого візуального роману."

