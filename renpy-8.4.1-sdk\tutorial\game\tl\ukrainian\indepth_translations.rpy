﻿
# game/indepth_translations.rpy:12
translate ukrainian translations_c4ef181f:

    # e "Ren'Py includes support for translating your game into languages other than the one it was originally written in."
    e "Ren'Py включає підтримку для перекладу вашої гри мовами, відмінними від тієї, якою вона була написана."

# game/indepth_translations.rpy:14
translate ukrainian translations_20b9a600:

    # e "This includes the translation of every string in the game, including dialogue, menu choice, and interface strings, and of images and other assets."
    e "Це включає в себе переклад кожного рядка в грі, включно з діалогами, вибором меню та рядками інтерфейсу, а також зображень та інших ресурсів."

# game/indepth_translations.rpy:16
translate ukrainian translations_07c7643c:

    # e "While <PERSON>'<PERSON><PERSON> can find dialogue and menu choice strings for you, you'll have to indicate which other strings need translation."
    e "Хоча Ren'Py може знайти для вас рядки діалогу та вибору меню, вам доведеться вказати, які інші рядки потребують перекладу."

# game/indepth_translations.rpy:20
translate ukrainian translations_317d73e5:

    # e "For example, here is how we define a character and her name."
    e "Наприклад, ось як ми визначаємо персонажа та його ім’я."

# game/indepth_translations.rpy:24
translate ukrainian translations_ab0f3c94:

    # e "To mark Lucy's name as translatable, we surround it by parentheses preceded by a single underscore."
    e "Щоб позначити ім’я Люсі як перекладне, ми беремо його в круглі дужки, перед якими ставиться одне підкреслення."

# game/indepth_translations.rpy:26
translate ukrainian translations_c81acfc7:

    # e "Notice how we don't translate the reddish color that we use for her name. That stays the same for all languages."
    e "Зверніть увагу, що ми не перекладаємо червонуватий колір, який використовуємо для її імені. Це залишається незмінним для всіх мов."

# game/indepth_translations.rpy:33
translate ukrainian translations_8272a0ef:

    # e "Once that's done, you can generate the translation files. That's done by going to the launcher, and clicking translate."
    e "Коли це буде зроблено, ви зможете створити файли перекладу. Це можна зробити, перейшовши до лаунчера та натиснувши 'Створити переклади'."

# game/indepth_translations.rpy:35
translate ukrainian translations_fde34832:

    # e "After you type in the name of the language you'll be translating to, choosing Generate Translations will scan your game and create translation files."
    e "Після того, як ви введете назву мови, на яку перекладатимете, виберіть 'Створити переклади', щоб відсканувати гру та створити файли перекладу."

# game/indepth_translations.rpy:37
translate ukrainian translations_e2ebb4af:

    # e "The files will be generated in game/tl/language, where language is the name of the language you typed in."
    e "Файли будуть створені в game/tl/language, де language — це назва мови, яку ви ввели."

# game/indepth_translations.rpy:39
translate ukrainian translations_28ec40b9:

    # e "You'll need to edit those files to include translations for everything in your game."
    e "Вам потрібно буде відредагувати ці файли, щоб включити переклади для всього у вашій грі."

# game/indepth_translations.rpy:41
translate ukrainian translations_f6d3fd2d:

    # e "If you want to localize image files, you can also place them in game/tl/language."
    e "Якщо ви хочете локалізувати файли зображень, ви також можете розмістити їх у game/tl/language."

# game/indepth_translations.rpy:48
translate ukrainian translations_71bf6e72:

    # e "If the default fonts used by the game do not support the language you are translating to, you will have to change them."
    e "Якщо стандартні шрифти, які використовуються в грі, не підтримують мову, на яку ви перекладаєте, вам доведеться змінити їх."

# game/indepth_translations.rpy:50
translate ukrainian translations_82c9748e:

    # e "The translate python statement can be used to set the values of gui variables to change the font."
    e "Оператор translate python можна використовувати для встановлення значень змінних графічного інтерфейсу для зміни шрифту."

# game/indepth_translations.rpy:52
translate ukrainian translations_a0042025:

    # e "The translate style statement sets style properties more directly."
    e "Оператор 'translate style' встановлює властивості стилю більш безпосередньо."

# game/indepth_translations.rpy:54
translate ukrainian translations_b10990ce:

    # e "If you need to add new files, such as font files, you can place them into the game/tl/language directory where Ren'Py will find them."
    e "Якщо вам потрібно додати нові файли, наприклад файли шрифтів, ви можете розмістити їх у каталозі game/tl/language, де Ren'Py їх знайде."

# game/indepth_translations.rpy:58
translate ukrainian translations_01fcacc2:

    # e "Finally, you'll have to add support for picking the language of the game. That usually goes into the preferences screen, found in screens.rpy."
    e "Тепер, вам доведеться додати підтримку вибору мови гри. Зазвичай це відбувається на екрані налаштувань у screens.rpy."

# game/indepth_translations.rpy:60
translate ukrainian translations_a91befcc:

    # e "Here's an excerpt of the preferences screen of this tutorial. The Language action tells Ren'Py to change the language. It takes a string giving a language name, or None."
    e "Ось уривок екрана налаштувань цього навчального посібника. Дія 'Launguage' повідомляє Ren'Py змінити мову. Потрібен рядок із назвою мови або None."

# game/indepth_translations.rpy:62
translate ukrainian translations_9b7d6401:

    # e "The None language is special, as it's the default language that the visual novel was written in. Since this tutorial was written in English, Language(None) selects English."
    e "Мова None є особливою, оскільки це мова за замовчуванням, якою було написано візуальний роман. Оскільки цей навчальний посібник було написано англійською мовою, 'Language(None)' вибирає англійську."

