﻿
# game/tutorial_atl.rpy:208
translate ukrainian tutorial_positions_a09a3fd1:

    # e "In this tutorial, I'll teach you how <PERSON><PERSON><PERSON><PERSON> positions things on the screen. But before that, let's learn a little bit about how <PERSON> handles numbers."
    e "У цьому навчальному посібнику я навчу вас, як Ren'Py розташовує елементи на екрані. Але перед цим давайте трохи дізнаємось про те, як Python обробляє числа."

# game/tutorial_atl.rpy:210
translate ukrainian tutorial_positions_ba39aabc:

    # e "There are two main kinds of numbers in Python: integers and floating point numbers. An integer consists entirely of digits, while a floating point number has a decimal point."
    e "У Python є два основних типи чисел: цілі числа та числа з плаваючою комою. Ціле число повністю складається з цифр, а число з плаваючою комою має десяткову кому."

# game/tutorial_atl.rpy:212
translate ukrainian tutorial_positions_a60b775d:

    # e "For example, 100 is an integer, while 0.5 is a floating point number, or float for short. In this system, there are two zeros: 0 is an integer, and 0.0 is a float."
    e "Наприклад, 100 є цілим числом, тоді як 0,5 є числом з плаваючою комою. У цій системі є два нулі: 0 — ціле число, а 0,0 — число з плаваючою точкою."

# game/tutorial_atl.rpy:214
translate ukrainian tutorial_positions_7f1a560c:

    # e "Ren'Py uses integers to represent absolute coordinates, and floats to represent fractions of an area with known size."
    e "Ren'Py використовує цілі числа для представлення абсолютних координат, а числа плаваючою комою для представлення частин області з відомим розміром."

# game/tutorial_atl.rpy:216
translate ukrainian tutorial_positions_8e7d3e52:

    # e "When we're positioning something, the area is usually the entire screen."
    e "Коли ми щось позиціонуємо, область зазвичай займає весь екран."

# game/tutorial_atl.rpy:218
translate ukrainian tutorial_positions_fdcf9d8b:

    # e "Let me get out of the way, and I'll show you where some positions are."
    e "Дозвольте мені відійти, і я покажу вам, де є деякі позиції."

# game/tutorial_atl.rpy:232
translate ukrainian tutorial_positions_76d7a5bf:

    # e "The origin is the upper-left corner of the screen. That's where the x position (xpos) and the y position (ypos) are both zero."
    e "Початкова точка - лівий верхній кут екрана. Ось де позиція x (xpos) і позиція y (ypos) дорівнюють нулю."

# game/tutorial_atl.rpy:238
translate ukrainian tutorial_positions_be14c7c3:

    # e "When we increase xpos, we move to the right. So here's an xpos of .5, meaning half the width across the screen."
    e "При збільшені xpos, ми рухаємося вправо. Отже, ось xpos .5, що означає половину ширини екрана."

# game/tutorial_atl.rpy:243
translate ukrainian tutorial_positions_9b91be6c:

    # e "Increasing xpos to 1.0 moves us to the right-hand border of the screen."
    e "Збільшення xpos до 1.0 переміщує нас до правої межі екрана."

# game/tutorial_atl.rpy:249
translate ukrainian tutorial_positions_2b293304:

    # e "We can also use an absolute xpos, which is given in an absolute number of pixels from the left side of the screen. For example, since this window is 1280 pixels across, using an xpos of 640 will return the target to the center of the top row."
    e "Ми також можемо використати абсолютну xpos, яка надається в абсолютній кількості пікселів з лівого боку екрана. Наприклад, оскільки це вікно має розмір 1280 пікселів, використання xpos 640 поверне ціль у центр верхнього рядка."

# game/tutorial_atl.rpy:251
translate ukrainian tutorial_positions_c4d18c0a:

    # e "The y-axis position, or ypos works the same way. Right now, we have a ypos of 0.0."
    e "Положення осі Y або ypos працює так само. Наразі ми маємо ypos 0,0."

# game/tutorial_atl.rpy:257
translate ukrainian tutorial_positions_16933a61:

    # e "Here's a ypos of 0.5."
    e "Ось тут ypos 0,5."

# game/tutorial_atl.rpy:262
translate ukrainian tutorial_positions_6eb36777:

    # e "A ypos of 1.0 specifies a position at the bottom of the screen. If you look carefully, you can see the position indicator spinning below the text window."
    e "ypos 1.0 визначає позицію внизу екрана. Якщо ви уважно подивитеся, ви побачите індикатор позиції, що обертається під текстовим вікном."

# game/tutorial_atl.rpy:264
translate ukrainian tutorial_positions_a423050f:

    # e "Like xpos, ypos can also be an integer. In this case, ypos would give the total number of pixels from the top of the screen."
    e "Як і xpos, ypos також може бути цілим числом. У цьому випадку ypos дасть загальну кількість пікселів від верхньої частини екрана."

# game/tutorial_atl.rpy:272
translate ukrainian tutorial_positions_bc7a809a:

    # e "Can you guess where this position is, relative to the screen?" nointeract
    e "Чи можете ви вгадати, де це положення відносно екрана?" nointeract

# game/tutorial_atl.rpy:276
translate ukrainian tutorial_positions_6f926e18:

    # e "Sorry, that's wrong. The xpos is .75, and the ypos is .25."
    e "Вибачте, це неправильно. Xpos становить 0,75, а ypos — 0,25."

# game/tutorial_atl.rpy:278
translate ukrainian tutorial_positions_5d5feb98:

    # e "In other words, it's 75%% of the way from the left side, and 25%% of the way from the top."
    e "Іншими словами, це 75%% шляху від лівого боку та 25%% шляху від верху."

# game/tutorial_atl.rpy:282
translate ukrainian tutorial_positions_77b45218:

    # e "Good job! You got that position right."
    e "Молодці! Ви вгадали правильну позицію."

# game/tutorial_atl.rpy:286
translate ukrainian tutorial_positions_6f926e18_1:

    # e "Sorry, that's wrong. The xpos is .75, and the ypos is .25."
    e "Вибачте, це неправильно. Xpos становить 0,75, а ypos — 0,25."

# game/tutorial_atl.rpy:288
translate ukrainian tutorial_positions_5d5feb98_1:

    # e "In other words, it's 75%% of the way from the left side, and 25%% of the way from the top."
    e "Іншими словами, це 75%% шляху від лівого боку та 25%% шляху від верху."

# game/tutorial_atl.rpy:302
translate ukrainian tutorial_positions_e4380a83:

    # e "The second position we care about is the anchor. The anchor is a spot on the thing being positioned."
    e "Друга позиція, яка нас цікавить, це прив'язка. Прив'язка - це місце на позиціонованому предметі."

# game/tutorial_atl.rpy:304
translate ukrainian tutorial_positions_d1db1246:

    # e "For example, here we have an xanchor of 0.0 and a yanchor of 0.0. It's in the upper-left corner of the logo image."
    e "Наприклад, тут ми маємо xanchor 0,0 і yanchor 0,0. Він знаходиться у верхньому лівому куті зображення логотипу."

# game/tutorial_atl.rpy:309
translate ukrainian tutorial_positions_6056873f:

    # e "When we increase the xanchor to 1.0, the anchor moves to the right corner of the image."
    e "Коли ми збільшуємо xanchor до 1,0, прив'язка переміщується в правий кут зображення."

# game/tutorial_atl.rpy:314
translate ukrainian tutorial_positions_7cdb8dcc:

    # e "Similarly, when both xanchor and yanchor are 1.0, the anchor is the bottom-right corner."
    e "Подібним чином, коли і xanchor, і yanchor мають значення 1,0, прив'язкою є нижній правий кут."

# game/tutorial_atl.rpy:321
translate ukrainian tutorial_positions_03a07da8:

    # e "To place an image on the screen, we need both the position and the anchor."
    e "Щоб розмістити зображення на екрані, нам потрібна і позиція, і прив'язка."

# game/tutorial_atl.rpy:329
translate ukrainian tutorial_positions_8945054f:

    # e "We then line them up, so that both the position and anchor are at the same point on the screen."
    e "Потім ми вирівнюємо їх так, щоб і позиція, і прив'язка були в одній точці на екрані."

# game/tutorial_atl.rpy:339
translate ukrainian tutorial_positions_2b184a93:

    # e "When we place both in the upper-left corner, the image moves to the upper-left corner of the screen."
    e "Коли ми розміщуємо обидва у верхньому лівому куті, зображення переміщується у верхній лівий кут екрана."

# game/tutorial_atl.rpy:348
translate ukrainian tutorial_positions_5aac4f3f:

    # e "With the right combination of position and anchor, any place on the screen can be specified, without even knowing the size of the image."
    e "За допомогою правильної комбінації позиції та прив’язки можна вказати будь-яке місце на екрані, навіть не знаючи розміру зображення."

# game/tutorial_atl.rpy:360
translate ukrainian tutorial_positions_3b59b797:

    # e "It's often useful to set xpos and xanchor to the same value. We call that xalign, and it gives a fractional position on the screen."
    e "Часто корисно встановити однакові значення для xpos і xanchor. Ми називаємо це xalign, і воно дає дробове положення на екрані."

# game/tutorial_atl.rpy:365
translate ukrainian tutorial_positions_b8ebf9fe:

    # e "For example, when we set xalign to 0.0, things are aligned to the left side of the screen."
    e "Наприклад, коли ми встановлюємо xalign на 0,0, елементи вирівнюються по лівій частині екрана."

# game/tutorial_atl.rpy:370
translate ukrainian tutorial_positions_8ce35d52:

    # e "When we set it to 1.0, then we're aligned to the right side of the screen."
    e "При встановлені значення 1.0, ми вирівнюємося по правій частині екрана."

# game/tutorial_atl.rpy:375
translate ukrainian tutorial_positions_6745825f:

    # e "And when we set it to 0.5, we're back to the center of the screen."
    e "І при встановлені значення 0,5, ми повертаємося до центру екрана."

# game/tutorial_atl.rpy:377
translate ukrainian tutorial_positions_64428a07:

    # e "Setting yalign is similar, except along the y-axis."
    e "Встановлення yalign аналогічно, за винятком уздовж осі y."

# game/tutorial_atl.rpy:379
translate ukrainian tutorial_positions_cfb77d42:

    # e "Remember that xalign is just setting xpos and xanchor to the same value, and yalign is just setting ypos and yanchor to the same value."
    e "Пам’ятайте, що xalign встановлює однакові значення для xpos і xanchor, а yalign встановлює однакові значення для ypos і yanchor."

# game/tutorial_atl.rpy:384
translate ukrainian tutorial_positions_cfc1723e:

    # e "The xcenter and ycenter properties position the center of the image. Here, with xcenter set to .75, the center of the image is three-quarters of the way to the right side of the screen."
    e "Параметри xcenter і ycenter позиціонують центр зображення. Тут, якщо xcenter встановлено на 0,75, центр зображення знаходиться на три чверті правого краю екрана."

# game/tutorial_atl.rpy:389
translate ukrainian tutorial_positions_7728dbf9:

    # e "The difference between xalign and xcenter is more obvious when xcenter is 1.0, and the image is halfway off the right side of the screen."
    e "Різниця між xalign і xcenter більш очевидна, коли xcenter дорівнює 1.0, а зображення знаходиться на півшляху від правого боку екрана."

# game/tutorial_atl.rpy:397
translate ukrainian tutorial_positions_1b1cedc6:

    # e "There are the xoffset and yoffset properties, which are applied after everything else, and offset things to the right or bottom, respectively."
    e "Існують параметри xoffset і yoffset, які застосовуються після всього іншого та зміщують речі вправо або вниз відповідно."

# game/tutorial_atl.rpy:402
translate ukrainian tutorial_positions_e6da2798:

    # e "Of course, you can use negative numbers to offset things to the left and top."
    e "Звичайно, ви можете використовувати від’ємні числа для зсуву вліво і вгору."

# game/tutorial_atl.rpy:407
translate ukrainian tutorial_positions_e0fe2d81:

    # e "Lastly, I'll mention that there are combined properties like align, pos, anchor, and center. Align takes a pair of numbers, and sets xalign to the first and yalign to the second. The others are similar."
    e "Нарешті, я згадаю, що існують такі комбіновані параметри, як align, pos, anchor та center. Align приймає пару чисел і встановлює xalign для першого, а yalign для другого. Інші так само схожі."

# game/tutorial_atl.rpy:414
translate ukrainian tutorial_positions_0f4ca2b6:

    # e "Once you understand positions, you can use transformations to move things around the Ren'Py screen."
    e "Коли ви зрозумієте позиції, ви зможете використовувати трансформації, щоб переміщувати речі на екрані Ren'Py."

# game/tutorial_atl.rpy:421
translate ukrainian tutorial_atl_d5d6b62a:

    # e "Ren'Py uses transforms to animate, manipulate, and place images. We've already seen the very simplest of transforms in use:"
    e "Ren'Py використовує трансформації для анімації, маніпулювання та розміщення зображень. Ми вже бачили використання найпростіших трансформацій:"

# game/tutorial_atl.rpy:428
translate ukrainian tutorial_atl_7e853c9d:

    # e "Transforms can be very simple affairs that place the image somewhere on the screen, like the right transform."
    e "Трансформації можуть бути дуже простими справами, які розміщують зображення десь на екрані, як at transform."

# game/tutorial_atl.rpy:432
translate ukrainian tutorial_atl_87a6ecbd:

    # e "But transforms can also be far more complicated affairs, that introduce animation and effects into the mix. To demonstrate, let's have a Gratuitous Rock Concert!"
    e "Але трансформації також можуть бути набагато складнішими, які вводять у суміш анімацію та ефекти. Щоб продемонструвати, давайте влаштуємо безоплатний рок-концерт!"

# game/tutorial_atl.rpy:440
translate ukrainian tutorial_atl_65badef3:

    # e "But first, let's have... a Gratuitous Rock Concert!"
    e "Але спершу давайте влаштуємо... безоплатний рок-концерт!"

# game/tutorial_atl.rpy:448
translate ukrainian tutorial_atl_e0d3c5ec:

    # e "That was a lot of work, but it was built out of small parts."
    e "Це була велика робота, але вона була побудована з маленьких деталей."

# game/tutorial_atl.rpy:450
translate ukrainian tutorial_atl_f2407514:

    # e "Most transforms in Ren'Py are built using the Animation and Transform Language, or ATL for short."
    e "Більшість трансформацій у Ren'Py створено за допомогою мови анімації та трансформації, або скорочено ATL."

# game/tutorial_atl.rpy:452
translate ukrainian tutorial_atl_1f22f875:

    # e "There are currently three places where ATL can be used in Ren'Py."
    e "Наразі існує три місця, де ATL можна використовувати в Ren'Py."

# game/tutorial_atl.rpy:457
translate ukrainian tutorial_atl_fd036bdf:

    # e "The first place ATL can be used is as part of an image statement. Instead of a displayable, an image may be defined as a block of ATL code."
    e "Перше місце, де ATL можна використовувати, це як частина оператора image. Замість відображуваного, зображення може бути визначено як блок коду ATL."

# game/tutorial_atl.rpy:459
translate ukrainian tutorial_atl_7cad2ab9:

    # e "When used in this way, we have to be sure that ATL includes one or more displayables to actually show."
    e "При такому використанні ми маємо бути впевнені, що ATL містить один або більше відображуваних елементів для фактичного показу."

# game/tutorial_atl.rpy:464
translate ukrainian tutorial_atl_c78b2a1e:

    # e "The second way is through the use of the transform statement. This assigns the ATL block to a python variable, allowing it to be used in at clauses and inside other transforms."
    e "Другий спосіб — це використання оператора transform. Це призначає блок ATL змінній python, дозволяючи використовувати його в пропозиціях at і всередині інших перетворень."

# game/tutorial_atl.rpy:476
translate ukrainian tutorial_atl_da7a7759:

    # e "Finally, an ATL block can be used as part of a show statement, instead of the at clause." id tutorial_atl_da7a7759
    e "І нарешті, блок ATL можна використовувати як частину оператора show замість пропозиції at." id tutorial_atl_da7a7759

# game/tutorial_atl.rpy:483
translate ukrainian tutorial_atl_1dd345c6:

    # e "When ATL is used as part of a show statement, values of properties exist even when the transform is changed. So even though your click stopped the motion, the image remains in the same place." id tutorial_atl_1dd345c6
    e "Коли ATL використовується як частина оператора show, значення параметрів існують, навіть коли трансформацію змінено. Таким чином, навіть якщо клацання зупинило рух, зображення залишається на тому самому місці." id tutorial_atl_1dd345c6

# game/tutorial_atl.rpy:491
translate ukrainian tutorial_atl_98047789:

    # e "The key to ATL is what we call composability. ATL is made up of relatively simple commands, which can be combined together to create complicated transforms."
    e "Ключем до ATL є те, що ми називаємо компонуванням. ATL складається з відносно простих команд, які можна комбінувати разом для створення складних трансформацій."

# game/tutorial_atl.rpy:493
translate ukrainian tutorial_atl_ed82983f:

    # e "Before I explain how ATL works, let me explain what animation and transformation are."
    e "Перш ніж пояснювати, як працює ATL, дозвольте мені пояснити, що таке анімація та трансформація."

# game/tutorial_atl.rpy:498
translate ukrainian tutorial_atl_2807adff:

    # e "Animation is when the displayable being shown changes. For example, right now I am changing my expression."
    e "Анімація - це коли відображуване зображення змінюється. Наприклад, зараз я зміню вираз обличчя."

# game/tutorial_atl.rpy:525
translate ukrainian tutorial_atl_3eec202b:

    # e "Transformation involves moving or distorting an image. This includes placing it on the screen, zooming it in and out, rotating it, and changing its opacity."
    e "Трансформація передбачає переміщення або спотворення зображення. Це включає розміщення його на екрані, збільшення та зменшення масштабу, обертання та зміну його непрозорості."

# game/tutorial_atl.rpy:533
translate ukrainian tutorial_atl_fbc9bf83:

    # e "To introduce ATL, let's start by looking at at a simple animation. Here's one that consists of five lines of ATL code, contained within an image statement."
    e "Щоб представити ATL, давайте почнемо з перегляду простої анімації. Ось один, який складається з п’яти рядків коду ATL, що міститься в операторі image."

# game/tutorial_atl.rpy:535
translate ukrainian tutorial_atl_bf92d973:

    # e "To change a displayable, simply mention it on a line of ATL. Here, we're switching back and forth between two images."
    e "Щоб змінити відображуваний, просто згадайте його в рядку ATL. Тут ми перемикаємося між двома зображеннями."

# game/tutorial_atl.rpy:537
translate ukrainian tutorial_atl_51a41db4:

    # e "Since we're defining an image, the first line of ATL must give a displayable. Otherwise, there would be nothing to show."
    e "Оскільки ми визначаємо зображення, перший рядок ATL має давати відображуваний. Інакше не було б чого показувати."

# game/tutorial_atl.rpy:539
translate ukrainian tutorial_atl_3d065074:

    # e "The second and fourth lines are pause statements, which cause ATL to wait half a second each before continuing. That's how we give the delay between images."
    e "Другий і четвертий рядок - це оператори pause, які змушують ATL чекати півсекунди перед тим, як продовжити. Таким чином ми створюємо затримку між зображеннями."

# game/tutorial_atl.rpy:541
translate ukrainian tutorial_atl_60f2a5e8:

    # e "The final line is a repeat statement. This causes the current block of ATL to be restarted. You can only have one repeat statement per block."
    e "Останній рядок - це оператор repeat. Це спричиняє перезапуск поточного блоку ATL. Ви можете мати лише один оператор повторення на блок."

# game/tutorial_atl.rpy:546
translate ukrainian tutorial_atl_146cf4c4:

    # e "If we were to write repeat 2 instead, the animation would loop twice, then stop."
    e "Якщо замість цього ми напишемо repeat 2, анімація зациклиться двічі, а потім зупиниться."

# game/tutorial_atl.rpy:551
translate ukrainian tutorial_atl_d90b1838:

    # e "Omitting the repeat statement means that the animation stops once we reach the end of the block of ATL code."
    e "Пропущення оператора repeat означає, що анімація припиняється, коли ми досягаємо кінця блоку коду ATL."

# game/tutorial_atl.rpy:557
translate ukrainian tutorial_atl_e5872360:

    # e "By default, displayables are replaced instantaneously. We can also use a with clause to give a transition between displayables."
    e "За замовчуванням показані елементи замінюються миттєво. Ми також можемо використовувати пропозицію with, щоб забезпечити перехід між відображуваними."

# game/tutorial_atl.rpy:564
translate ukrainian tutorial_atl_2e9d63ea:

    # e "With animation done, we'll see how we can use ATL to transform images, starting with positioning an image on the screen."
    e "Після завершення анімації ми побачимо, як ми можемо використовувати ATL для трансформації зображень, починаючи з позиціонування зображення на екрані."

# game/tutorial_atl.rpy:573
translate ukrainian tutorial_atl_ddc55039:

    # e "The simplest thing we can do is to statically position an image. This is done by giving the names of the position properties, followed by the property values." id tutorial_atl_ddc55039
    e "Найпростіше, що ми можемо зробити, це статичне розташування зображення. Це робиться за допомогою назви параметрів позиції, а потім значення параметрів." id tutorial_atl_ddc55039

# game/tutorial_atl.rpy:578
translate ukrainian tutorial_atl_43516492:

    # e "With a few more statements, we can move things around on the screen."
    e "Додавши ще кілька операторів, ми зможемо рухати речі на екрані."

# game/tutorial_atl.rpy:580
translate ukrainian tutorial_atl_fb979287:

    # e "This example starts the image off at the top-right of the screen, and waits a second. It then moves it to the left side, waits another second, and repeats."
    e "У цьому прикладі зображення вимикається у верхньому правому куті екрана та чекає секунду. Потім він переміщує його вліво, чекає ще секунду та повторює."

# game/tutorial_atl.rpy:582
translate ukrainian tutorial_atl_7650ec09:

    # e "The pause and repeat statements are the same statements we used in our animations. They work throughout ATL code."
    e "Оператори pause і repeat — це ті самі оператори, які ми використовували в наших анімаціях. Вони працюють у всьому коді ATL."

# game/tutorial_atl.rpy:587
translate ukrainian tutorial_atl_d3416d4f:

    # e "Having the image jump around on the screen isn't all that useful. That's why ATL has the interpolation statement."
    e "Зображення, яке стрибає по екрану, не дуже корисно. Ось чому ATL має оператор interpolation."

# game/tutorial_atl.rpy:589
translate ukrainian tutorial_atl_4e7512ec:

    # e "The interpolation statement allows you to smoothly vary the value of a transform property, from an old to a new value."
    e "Оператор interpolation дозволяє плавно змінювати значення властивості трансформації від старого до нового значення."

# game/tutorial_atl.rpy:591
translate ukrainian tutorial_atl_685eeeaa:

    # e "Here, we have an interpolation statement on the second ATL line. It starts off with the name of a time function, in this case linear."
    e "Тут ми маємо оператор interpolation у другому рядку ATL. Він починається з назви функції часу(linear)."

# game/tutorial_atl.rpy:593
translate ukrainian tutorial_atl_c5cb49de:

    # e "That's followed by an amount of time, in this case three seconds. It ends with a list of properties, each followed by its new value."
    e "Після цього йде час, у цьому випадку три секунди. Він закінчується списком властивостей, після кожного з яких вказується нове значення."

# game/tutorial_atl.rpy:595
translate ukrainian tutorial_atl_04b8bc1d:

    # e "The value of each property is interpolated from its value when the statement starts to the value at the end of the statement. This is done once per frame, allowing smooth animation."
    e "Значення кожної властивості інтерполюється від його значення на початку оператора до значення в кінці оператора. Це робиться один раз на кадр, що забезпечує плавну анімацію."

# game/tutorial_atl.rpy:606
translate ukrainian tutorial_atl_2958f397:

    # e "ATL supports more complicated move types, like circle and spline motion. But I won't be showing those here."
    e "ATL підтримує більш складні типи переміщень, як рух по колу та сплайн. Але я не буду їх тут показувати."

# game/tutorial_atl.rpy:610
translate ukrainian tutorial_atl_d08fe8d9:

    # e "Apart from displayables, pause, interpolation, and repeat, there are a few other statements we can use as part of ATL."
    e "Крім displayables, pause, interpolation і repeat, є кілька інших операторів, які ми можемо використовувати як частину ATL."

# game/tutorial_atl.rpy:622
translate ukrainian tutorial_atl_84b22ac0:

    # e "ATL transforms created using the statement become ATL statements themselves. Since the default positions are also transforms, this means that we can use left, right, and center inside of an ATL block."
    e "Трансформації ATL, створені за допомогою оператора, самі стають операторами ATL. Оскільки позиція за замовчуванням також є трансформаціями, це означає, що ми можемо використовувати left, right і center всередині блоку ATL."

# game/tutorial_atl.rpy:638
translate ukrainian tutorial_atl_331126c1:

    # e "Here, we have two new statements. The block statement allows you to include a block of ATL code. Since the repeat statement applies to blocks, this lets you repeat only part of an ATL transform."
    e "Тут ми маємо два нових оператори. Оператор block дозволяє включити блок коду ATL. Оскільки оператор repeat застосовується до блоків, це дозволяє повторювати лише частину перетворення ATL."

# game/tutorial_atl.rpy:640
translate ukrainian tutorial_atl_24f67b67:

    # e "We also have the time statement, which runs after the given number of seconds have elapsed from the start of the block. It will run even if another statement is running, stopping the other statement."
    e "У нас також є оператор time, який запускається після закінчення заданої кількості секунд від початку блоку. Він буде працювати, навіть якщо виконується інший оператор, зупиняючи інший оператор."

# game/tutorial_atl.rpy:642
translate ukrainian tutorial_atl_b7709507:

    # e "So this example bounces the image back and forth for eleven and a half seconds, and then moves it to the right side of the screen."
    e "Отже, у цьому прикладі зображення повертається вперед і назад протягом одинадцяти з половиною секунд, а потім переміщується в праву частину екрана."

# game/tutorial_atl.rpy:656
translate ukrainian tutorial_atl_f903bc3b:

    # e "The parallel statement lets us run two blocks of ATL code at the same time."
    e "Оператор parallel дозволяє запускати два блоки коду ATL одночасно."

# game/tutorial_atl.rpy:658
translate ukrainian tutorial_atl_5d0f8f9d:

    # e "Here, the top block move the image in the horizontal direction, and the bottom block moves it in the vertical direction. Since they're moving at different speeds, it looks like the image is bouncing on the screen."
    e "Тут верхній блок переміщує зображення в горизонтальному напрямку, а нижній блок переміщує його у вертикальному напрямку. Оскільки вони рухаються з різною швидкістю, здається, що зображення підстрибує на екрані."

# game/tutorial_atl.rpy:672
translate ukrainian tutorial_atl_28a7d27e:

    # e "Finally, the choice statement makes Ren'Py randomly pick a block of ATL code. This allows you to add some variation as to what Ren'Py shows."
    e "І нарешті, оператор choice змушує Ren'Py випадково вибрати блок коду ATL. Це дозволяє додати деякі варіації до того, що показує Ren'Py."

# game/tutorial_atl.rpy:678
translate ukrainian tutorial_atl_2265254b:

    # e "This tutorial game has only scratched the surface of what you can do with ATL. For example, we haven't even covered the on and event statements. For more information, you might want to check out {a=https://renpy.org/doc/html/atl.html}the ATL chapter in the reference manual{/a}."
    e "У цьому навчальному посібнику ми лише позначили те, що можна робити за допомогою ATL. Наприклад, ми навіть не взяли оператора on і event. Щоб дізнатися більше, перегляньте розділ про ATL у {a=https://renpy.org/doc/html/atl.html}довідковому посібнику{/a}."

# game/tutorial_atl.rpy:687
translate ukrainian transform_properties_391169cf:

    # e "Ren'Py has quite a few transform properties that can be used with ATL, the Transform displayable, and the add Screen Language statement."
    e "Ren'Py має чимало параметрів трансформації, які можна використовувати з ATL, відображуваним Transform і оператором add Screen Language."

# game/tutorial_atl.rpy:688
translate ukrainian transform_properties_fc895a1f:

    # e "Here, we'll show them off so you can see them in action and get used to what each does."
    e "Тут ми покажемо їх, щоб ви могли побачити їх у дії та звикнути до того, що кожен робить."

# game/tutorial_atl.rpy:704
translate ukrainian transform_properties_88daf990:

    # e "First off, all of the position properties are also transform properties. These include the pos, anchor, align, center, and offset properties."
    e "По-перше, усі властивості позиції також є властивостями трансформації. До них належать властивості pos, anchor, align, center та offset."

# game/tutorial_atl.rpy:722
translate ukrainian transform_properties_d7a487f1:

    # e "The position properties can also be used to pan over a displayable larger than the screen, by giving xpos and ypos negative values."
    e "Властивості позиції також можна використовувати для панорамування відображуваного, більшого за екран, шляхом надання від’ємних значень xpos та ypos."

# game/tutorial_atl.rpy:732
translate ukrainian transform_properties_89e0d7c2:

    # "The subpixel property controls how things are lined up with the screen. When False, images can be pixel-perfect, but there can be pixel jumping."
    "Властивість subpixel керує тим, як елементи вирівнюються на екрані. Якщо значення False, зображення можуть бути ідеальними для пікселів, але можливі стрибки пікселів."

# game/tutorial_atl.rpy:739
translate ukrainian transform_properties_4194527e:

    # "When it's set to True, movement is smoother at the cost of blurring images a little."
    "При встановлені значення True, об'єкт стає плавнішим за рахунок невеликого розмивання зображень."

# game/tutorial_atl.rpy:758
translate ukrainian transform_properties_35934e77:

    # e "Transforms also support polar coordinates. The around property sets the center of the coordinate system to coordinates given in pixels."
    e "Трансформація також підтримує систему полярних координатів. Властивість around встановлює центр системи координат у координатах, заданих у пікселях."

# game/tutorial_atl.rpy:766
translate ukrainian transform_properties_605ebd0c:

    # e "The angle property gives the angle in degrees. Angles run clockwise, with the zero angle at the top of the screen."
    e "Властивість angle визначає кут у градусах. Кути рухаються за годинниковою стрілкою, якщо кут 0 то у верхній частині екрана."

# game/tutorial_atl.rpy:775
translate ukrainian transform_properties_6d4555ed:

    # e "The radius property gives the distance in pixels from the anchor of the displayable to the center of the coordinate system."
    e "Властивість radius дає відстань у пікселях від прив’язки відображуваного до центру системи координат."

# game/tutorial_atl.rpy:789
translate ukrainian transform_properties_7af037a5:

    # e "There are several ways to resize a displayable. The zoom property lets us scale a displayable by a factor, making it bigger and smaller."
    e "Є кілька способів змінити розмір відображуваного. Властивість zoom дозволяє нам масштабувати відображуваний у певний коефіцієнт, роблячи його більшим і меншим."

# game/tutorial_atl.rpy:802
translate ukrainian transform_properties_b6527546:

    # e "The xzoom and yzoom properties allow the displayable to be scaled in the X and Y directions independently."
    e "Властивості xzoom і yzoom дозволяють незалежно масштабувати відображуваний у напрямках X і Y."

# game/tutorial_atl.rpy:812
translate ukrainian transform_properties_b98b780b:

    # e "By making xzoom or yzoom a negative number, we can flip the image horizontally or vertically."
    e "Зробивши xzoom або yzoom від’ємним числом, ми можемо перевернути зображення горизонтально або вертикально."

# game/tutorial_atl.rpy:822
translate ukrainian transform_properties_74d542ff:

    # e "Instead of zooming by a scale factor, the size transform property can be used to scale a displayable to a size in pixels."
    e "Замість масштабування за допомогою scale factor, можна використовувати властивість size transform для масштабування відображуваного до розміру в пікселях."

# game/tutorial_atl.rpy:837
translate ukrainian transform_properties_438ed776:

    # e "The alpha property is used to change the opacity of a displayable. This can make it appear and disappear."
    e "Властивість alpha використовується для зміни прозорості відображуваного. Це може змусити його з'являтися і зникати."

# game/tutorial_atl.rpy:850
translate ukrainian transform_properties_aee19f86:

    # e "The rotate property rotates a displayable."
    e "Властивість rotate повертає відображуваний об'єкт."

# game/tutorial_atl.rpy:861
translate ukrainian transform_properties_57b3235a:

    # e "By default, when a displayable is rotated, Ren'Py will include extra space on all four sides, so the size doesn't change as it rotates. Here, you can see the extra space on the left and top, and it's also there on the right and bottom."
    e "За замовчуванням, коли відображуваний об’єкт обертається, Ren'Py включатиме додатковий простір з усіх чотирьох сторін, тому розмір не змінюється під час обертання. Тут ви можете побачити додатковий простір ліворуч і вгорі, а також праворуч і внизу."

# game/tutorial_atl.rpy:873
translate ukrainian transform_properties_66d29ee8:

    # e "By setting rotate_pad to False, we can get rid of the space, at the cost of the size of the displayable changing as it rotates."
    e "Встановивши для rotate_pad значення False, ми можемо позбутися простору за рахунок зміни розміру відображуваного під час обертання."

# game/tutorial_atl.rpy:884
translate ukrainian transform_properties_7f32e8ad:

    # e "The tile transform properties, xtile and ytile, repeat the displayable multiple times."
    e "Властивості tile transform, xtile і ytile, повторюють відображувані кілька разів."

# game/tutorial_atl.rpy:894
translate ukrainian transform_properties_207b7fc8:

    # e "The crop property crops a rectangle out of a displayable, showing only part of it."
    e "Властивість crop обрізає прямокутник із відображуваного, показуючи лише його частину."

# game/tutorial_atl.rpy:908
translate ukrainian transform_properties_e7e22d28:

    # e "When used together, crop and size can be used to focus in on specific parts of an image."
    e "При спільному використанні crop та size можна використовувати для фокусування на певних частинах зображення."

# game/tutorial_atl.rpy:920
translate ukrainian transform_properties_f34abd82:

    # e "The xpan and ypan properties can be used to pan over a displayable, given an angle in degrees, with 0 being the center."
    e "Властивості xpan і ypan можна використовувати для панорамування над відображуваним, заданим кутом у градусах, де 0 є центром."

# game/tutorial_atl.rpy:927
translate ukrainian transform_properties_bfa3b139:

    # e "Those are all the transform properties we have to work with. By putting them together in the right order, you can create complex things."
    e "Це всі властивості трансформації, з якими нам потрібно працювати. Розміщуючи їх у правильному порядку, можна створювати складні речі."

translate ukrainian strings:

    # game/tutorial_atl.rpy:270
    old "xpos 1.0 ypos .5"
    new "xpos 1.0 ypos .5"

    # game/tutorial_atl.rpy:270
    old "xpos .75 ypos .25"
    new "xpos .75 ypos .25"

    # game/tutorial_atl.rpy:270
    old "xpos .25 ypos .33"
    new "xpos .25 ypos .33"

