﻿
# game/indepth_minigame.rpy:220
translate ukrainian demo_minigame_8f14835c:

    # e "You may want to mix Ren'Py with other forms of gameplay. There are a couple of ways to do this."
    e "Ви можете поєднати Ren'Py з іншими формами ігрового процесу. Є кілька способів зробити це."

# game/indepth_minigame.rpy:222
translate ukrainian demo_minigame_9b01503e:

    # e "The first is with the screen system, which can be used to display data and create button and menu based interfaces."
    e "Перший - це екранна система, яку можна використовувати для відображення даних і створення інтерфейсів на основі кнопок і меню."

# game/indepth_minigame.rpy:224
translate ukrainian demo_minigame_3e601161:

    # e "Screens will work for many simulation-style games and RPGs."
    e "Екрани будуть працювати для багатьох симуляторів і рольових ігор."

# game/indepth_minigame.rpy:226
translate ukrainian demo_minigame_a92baa6b:

    # e "When screens are not enough, you can write a creator-defined displayable to extend Ren'Py itself. A creator-defined displayable can process raw events and draw to the screen." id demo_minigame_a92baa6b
    e "Якщо екранів недостатньо, ви можете написати визначений автором відображуваний, щоб розширити сам Ren'Py. Визначений автором відображуваний елемент може обробляти необроблені події та малювати на екрані." id demo_minigame_a92baa6b

# game/indepth_minigame.rpy:228
translate ukrainian demo_minigame_a07dbae0:

    # e "That makes it possible to create all kinds of minigames. Would you like to play some pong?"
    e "Це дає змогу створювати різноманітні міні-ігри. Хочете пограти в понг?"

# game/indepth_minigame.rpy:245
translate ukrainian play_pong_ce00ff63:

    # e "I win!"
    e "Я перемогла!"

# game/indepth_minigame.rpy:249
translate ukrainian play_pong_68c82e98:

    # e "You won! Congratulations."
    e "Ви виграли! Вітаю."

# game/indepth_minigame.rpy:256
translate ukrainian pong_done_dde7e31a:

    # e "Would you like to play again?" nointeract
    e "Чи хотіли б ви зіграти знову?" nointeract

# game/indepth_minigame.rpy:268
translate ukrainian pong_done_a21abf38:

    # e "Here's the source code for the minigame. It's very complex, and assumes you understand Python well."
    e "Ось вихідний код міні-ігри. Він дуже складний і передбачає, що ви добре розумієте Python."

# game/indepth_minigame.rpy:270
translate ukrainian pong_done_750092ed:

    # e "I won't go over it in detail here. You can read more about it in the {a=https://www.renpy.org/doc/html/udd.html}Creator-Defined Displayable documentation{/a}."
    e "Я не буду розглядати це тут детально. Ви можете прочитати більше про це в {a=https://www.renpy.org/doc/html/udd.html}документації Creator-Defined Displayable{/a}."

# game/indepth_minigame.rpy:274
translate ukrainian pong_done_5781d902:

    # e "Minigames can spice up your visual novel, but be careful - not every visual novel player wants to be good at arcade games."
    e "Міні-ігри можуть оживити ваш візуальний роман, але будьте обережні - не кожен гравець у візуальному романі хоче добре грати в аркадні ігри."

# game/indepth_minigame.rpy:276
translate ukrainian pong_done_631325c8:

    # e "Part of the reason Ren'Py works well is that it's meant for certain types of games, like visual novels and life simulations."
    e "Однією з причин, чому Ren'Py добре працює, є те, що він призначений для певних типів ігор, як-от візуальні романи та симуляції життя."

# game/indepth_minigame.rpy:278
translate ukrainian pong_done_61d60761:

    # e "The further afield you get from those games, the more you'll find yourself fighting Ren'Py. At some point, it makes sense to consider other engines."
    e "Чим далі ви відійдете від цих ігор, тим більше ви будете сваритися з Ren'Py. У якийсь момент є сенс розглянути інші двигуни."

# game/indepth_minigame.rpy:282
translate ukrainian pong_done_715c7b12:

    # e "And that's fine with us. We'll always be here for you when you're making visual novels."
    e "І це добре для нас. Ми завжди будемо поруч, коли ви створюватимете візуальні романи."

translate ukrainian strings:

    # game/indepth_minigame.rpy:198
    old "Player"
    new "Гравець"

    # game/indepth_minigame.rpy:211
    old "Click to Begin"
    new "Клацніть щоб почати"

    # game/indepth_minigame.rpy:255
    old "Sure."
    new "Звичайно."

    # game/indepth_minigame.rpy:255
    old "No thanks."
    new "Ні, дякую."

