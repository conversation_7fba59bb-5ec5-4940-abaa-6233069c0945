﻿
# game/tutorial_quickstart.rpy:28
translate ukrainian tutorial_create_27048c11:

    # e "When you're ready to use <PERSON>'<PERSON><PERSON> to create your visual novel, the first step is to create a new project."
    e "Коли ви будете готові використовувати Ren'Py для створення свого візуального роману, першим кроком буде створення нового проєкту."

# game/tutorial_quickstart.rpy:30
translate ukrainian tutorial_create_bae6289c:

    # e "You can create a new project by clicking 'Create New Project' on the front screen of the launcher."
    e "Ви можете створити новий проєкт, натиснувши 'Створити новий проєкт' на головному меню лаунчера."

# game/tutorial_quickstart.rpy:32
translate ukrainian tutorial_create_45915fcb:

    # e "If this is your first time using Ren'P<PERSON>, it'll ask you for the place you want to keep your projects. The best place is always somewhere that's frequently backed up."
    e "Якщо ви вперше використовуєте Ren'Py, він запитає вас про те, де ви хочете зберігати свої проєкти. Найкраще місце завжди те, де часто створюють резервні копії."

# game/tutorial_quickstart.rpy:36
translate ukrainian tutorial_create_55e30cb5:

    # e "After that, Ren'Py will ask for a name for your project. You'll have to stick to English letters and numbers, as zip files can't handle anything more than that."
    e "Після цього Ren'Py попросить назвати ваш проєкт. Вам доведеться дотримуватися англійських літер і цифр, оскільки файли zip не можуть обробляти нічого, крім цього."

# game/tutorial_quickstart.rpy:40
translate ukrainian tutorial_create_dea3e5c2:

    # e "The next thing Ren'Py will ask for is the resolution the visual novel will run at. This controls how large or small you'll have to make your game's artwork."
    e "Наступне, про що запитає Ren'Py, це роздільна здатність візуального роману. Це визначає, наскільки великий чи малий ви будете мати для створення ілюстрації вашої гри."

# game/tutorial_quickstart.rpy:44
translate ukrainian tutorial_create_3289ea75:

    # e "Finally, Ren'Py will ask you to select a color scheme. You can change this after the game has been created, so just pick a color that's pleasing."
    e "Тепер Ren'Py попросить вас вибрати колірну схему. Ви можете змінити це після створення гри, тому просто виберіть колір, який вам подобається."

# game/tutorial_quickstart.rpy:48
translate ukrainian tutorial_create_6b9e3b96:

    # e "Once that's done, Ren'Py will work for a bit and return you to the main menu with the new project selected. Now, when you click Launch, Ren'Py will start your new game."
    e "Після цього Ren'Py попрацює деякий час і поверне вас до головного меню з вибраним новим проєктом. Тепер, коли ви натиснете 'Запустити проєкт', Ren'Py запустить вашу нову гру."

# game/tutorial_quickstart.rpy:50
translate ukrainian tutorial_create_bdf94f9b:

    # e "To get back here, you can choose 'Tutorial' to switch to this tutorial game."
    e "Щоб повернутися сюди, ви можете вибрати 'Навчальний посібник', щоб перейти до цієї гри-підручника."

# game/tutorial_quickstart.rpy:52
translate ukrainian tutorial_create_22f516df:

    # e "You'll also need to edit the games script to make changes. To do that, click 'script.rpy' on the front page of the launcher."
    e "Вам також потрібно буде відредагувати скрипт гри, щоб внести зміни. Для цього клацніть 'script.rpy' на головному меню лаунчера."

# game/tutorial_quickstart.rpy:54
translate ukrainian tutorial_create_9151025b:

    # e "If it's your first time doing so, Ren'Py will ask you to select a text editor. Atom might be a safe choice, but read the descriptions to be sure."
    e "Якщо ви робите це вперше, Ren'Py попросить вас вибрати текстовий редактор. Atom може бути безпечним вибором, але прочитайте описи, щоб переконатися."

# game/tutorial_quickstart.rpy:56
translate ukrainian tutorial_create_bfbd6220:

    # e "After the text editor is downloaded, the script will open up and you can start to change what characters are saying."
    e "Після завантаження текстового редактора скрипт відкриється, і ви зможете змінити те, що говорять персонажі."

# game/tutorial_quickstart.rpy:69
translate ukrainian tutorial_dialogue_112ff505:

    # e "Probably the most common thing a creator does with Ren'Py is to write dialogue for the player to read."
    e "Мабуть, найпоширеніша річ, яку творець робить із Ren'Py, — це написання діалогів для читання гравцем."

# game/tutorial_quickstart.rpy:71
translate ukrainian tutorial_dialogue_be2be31a:

    # e "But before I can show you how to write dialogue, let me show you how we present script examples."
    e "Але перш ніж я зможу показати вам, як писати діалоги, дозвольте мені показати вам, як ми представляємо приклади скрипта."

# game/tutorial_quickstart.rpy:74
translate ukrainian tutorial_dialogue_7b6be28e:

    # "Eileen" "Examples will show up in a window like the one above. You'll need to click outside of the example window in order to advance the tutorial."
    "Ейлін" "Приклади відображатимуться у вікні, подібному до наведеного вище. Вам потрібно буде клацнути за межами вікна прикладу, щоб перейти до посібника."

# game/tutorial_quickstart.rpy:76
translate ukrainian tutorial_dialogue_5269d005:

    # "Eileen" "When an example is bigger than the screen, you can scroll around in it using the mouse wheel or by simply dragging the mouse."
    "Ейлін" "Якщо приклад більший за екран, ви можете прокручувати його за допомогою коліщатка миші або просто перетягуючи мишу."

# game/tutorial_quickstart.rpy:78
translate ukrainian tutorial_dialogue_241c0c74:

    # "Eileen" "Script might seem scary at first, but if you look you'll see it's easy to match it up to what I'm saying."
    "Ейлін" "Спочатку скрипт може здатися страшним, але якщо ви подивитесь, то побачите, що його легко зіставити з тим, що я кажу."

# game/tutorial_quickstart.rpy:82
translate ukrainian tutorial_dialogue_f0d66410:

    # e "Let's see the simplest possible Ren'Py game."
    e "Давайте розглянемо найпростішу гру Ren'Py."

# game/tutorial_quickstart.rpy:89
translate ukrainian tutorial_dialogue_3e6b0068:

    # "Wow, it's really really dark in here."
    "Вау, тут справді дуже темно."

# game/tutorial_quickstart.rpy:91
translate ukrainian tutorial_dialogue_5072a404:

    # "Lucy" "Better watch out. You don't want to be eaten by a Grue."
    "Люсі" "Краще стережися. Ти ж не хочеш, щоб тебе з'їв Гру."

# game/tutorial_quickstart.rpy:99
translate ukrainian tutorial_dialogue_d39d1b2b:

    # e "I'll show you the script of that example."
    e "Я покажу вам скрипт цього прикладу."

# game/tutorial_quickstart.rpy:101
translate ukrainian tutorial_dialogue_f51ecf1f:

    # e "This script demonstrates two kinds of Ren'Py statements, labels and say statements."
    e "Цей скрипт демонструє два типи операторів Ren'Py, операторів label і say."

# game/tutorial_quickstart.rpy:103
translate ukrainian tutorial_dialogue_bc7ec147:

    # e "The first line is a label statement. The label statement is used to give a name to a place in the program."
    e "Перший рядок - це оператор label. Оператор label використовується для надання назви місцю в програмі."

# game/tutorial_quickstart.rpy:105
translate ukrainian tutorial_dialogue_b20db833:

    # e "In this case, we're naming a place \"start\". The start label is special, as it marks the place a game begins running."
    e "У цьому випадку ми називаємо місце \"start\". label start є особливим, оскільки він позначає місце початку гри."

# game/tutorial_quickstart.rpy:107
translate ukrainian tutorial_dialogue_b0afbe96:

    # e "The next line is a simple say statement. It consists of a string beginning with a double-quote, and ending at the next double-quote."
    e "Наступний рядок є простим оператором say. Він складається з рядка, що починається з подвійних лапок і закінчується наступною подвійною лапкою."

# game/tutorial_quickstart.rpy:109
translate ukrainian tutorial_dialogue_628c9e4c:

    # e "Special characters in strings can be escaped with a backslash. To include \" in a string, we have to write \\\"."
    e "Спеціальні символи в рядках можна екранувати за допомогою зворотної косої риски. Щоб включити \" в рядок, ми повинні написати \\\"."

# game/tutorial_quickstart.rpy:116
translate ukrainian tutorial_dialogue_3e6b0068_1:

    # "Wow, it's really really dark in here."
    "Вау, тут справді дуже темно."

# game/tutorial_quickstart.rpy:125
translate ukrainian tutorial_dialogue_d7f0b5b7:

    # e "When Ren'Py sees a single string on a line by itself, it uses the narrator to say that string. So a single string can be used to express a character's thoughts."
    e "Коли Ren'Py бачить окремий рядок у рядку, він використовує диктора, щоб вимовити цей рядок. Таким чином, один рядок може бути використаний для вираження думок персонажа."

# game/tutorial_quickstart.rpy:131
translate ukrainian tutorial_dialogue_5072a404_1:

    # "Lucy" "Better watch out. You don't want to be eaten by a Grue."
    "Lucy" "Краще стережися. Ти ж не хочеш, щоб тебе з'їв Гру."

# game/tutorial_quickstart.rpy:139
translate ukrainian tutorial_dialogue_9dd2d543:

    # e "When we have two strings separated by a space, the first is used as the character's name, and the second is what the character is saying."
    e "Коли у нас є два рядки, розділені пробілом, перший використовується як ім’я персонажа, а другий - це те, що персонаж говорить."

# game/tutorial_quickstart.rpy:141
translate ukrainian tutorial_dialogue_64ffe685:

    # e "This two-argument form of the say statement is used for dialogue, where a character is speaking out loud."
    e "Ця двоаргументна форма висловлювання say використовується для діалогу, де персонаж говорить вголос."

# game/tutorial_quickstart.rpy:143
translate ukrainian tutorial_dialogue_97a33275:

    # e "If you'd like, you can run this game yourself by erasing everything in your project's script.rpy file, and replacing it with the code in the box above."
    e "Якщо ви хочете, ви можете запустити цю гру самостійно, стерши все у файлі script.rpy вашого проєкту та замінивши його кодом у полі вище."

# game/tutorial_quickstart.rpy:145
translate ukrainian tutorial_dialogue_c5e70d7e:

    # e "Be sure to preserve the spacing before lines. That's known as indentation, and it's used to help Ren'Py group lines of script into blocks."
    e "Обов’язково зберігайте інтервали перед рядками. Це відомо як відступ, і він використовується, щоб допомогти Ren'Py групувати рядки скрипту в блоки."

# game/tutorial_quickstart.rpy:149
translate ukrainian tutorial_dialogue_90719f73:

    # e "Using a string for a character's name is inconvenient, for two reasons."
    e "Використання рядка для імені персонажа незручно з двох причин."

# game/tutorial_quickstart.rpy:151
translate ukrainian tutorial_dialogue_910f286a:

    # e "The first is that it's a bit verbose. While typing \"Lucy\" isn't so bad, imagine if you had to type \"Eileen Richardson\" thousands of times."
    e "По-перше, це трохи багатослівно. Хоча введення \"Люсі\" не таке вже й погане, уявіть, якби вам довелося вводити \"Ейлін Річардсон\" тисячі разів."

# game/tutorial_quickstart.rpy:153
translate ukrainian tutorial_dialogue_9c9d59c2:

    # e "The second is that it doesn't leave any place to put styling, which can change the look of a character."
    e "По-друге, він не залишає місця для стилізації, яка може змінити вигляд персонажа."

# game/tutorial_quickstart.rpy:155
translate ukrainian tutorial_dialogue_2a2d1e51:

    # e "To solve these problems, Ren'Py lets you define Characters."
    e "Щоб вирішити ці проблеми, Ren'Py дозволяє визначити Characters."

# game/tutorial_quickstart.rpy:159
translate ukrainian tutorial_dialogue_16e8c5fd:

    # e "Here's an example Character definition. It begins with the word \"define\". That tells Ren'Py that we are defining something."
    e "Ось приклад визначення Character. Воно починається зі слова \"define\". Це говорить Ren'Py, що ми щось визначаємо."

# game/tutorial_quickstart.rpy:161
translate ukrainian tutorial_dialogue_34fe5aa0:

    # e "Define is followed by a short name for the character, like \"l\". We'll be able to use that short name when writing dialogue."
    e "За визначенням йде коротке ім’я персонажу, наприклад \"l\". Ми зможемо використовувати цю коротку назву під час написання діалогу."

# game/tutorial_quickstart.rpy:163
translate ukrainian tutorial_dialogue_67f90201:

    # e "This is followed by an equals sign, and the thing that we're defining. In this case, it's a Character."
    e "За ним йде знак рівності та те, що ми визначаємо. У цьому випадку це Character."

# game/tutorial_quickstart.rpy:165
translate ukrainian tutorial_dialogue_4e454a89:

    # e "On the first line, the character's name is given to be \"Lucy\", and her name will be drawn a reddish color."
    e "У першому рядку вказано ім'я персонажа \"Люсі\", а її ім'я буде позначено червонуватим кольором."

# game/tutorial_quickstart.rpy:167
translate ukrainian tutorial_dialogue_db11f026:

    # e "These short names are case-sensitive. Capital L is a different name from lower-case l, so you'll need to be careful about that."
    e "Ці короткі назви чутливі до регістру. Велика L - це назва, яка відрізняється від малої l, тому вам потрібно бути обережним."

# game/tutorial_quickstart.rpy:171
translate ukrainian tutorial_dialogue_1d161320:

    # e "Now that we have a character defined, we can use it to say dialogue."
    e "Тепер, коли ми визначили character, ми можемо використовувати його, щоб говорити в діалозі."

# game/tutorial_quickstart.rpy:178
translate ukrainian tutorial_dialogue_3710169c:

    # l "Why are you trying to put words into my mouth? And who are you calling \"it\"?"
    l "Чому ти намагаєшся вкласти мені слова в уста? А кого ти називаєш \"це\"?"

# game/tutorial_quickstart.rpy:180
translate ukrainian tutorial_dialogue_6d463776:

    # l "What's more, what are you going to do about the Grue problem? Are you just going to leave me here?"
    l "Більше того, що ви збираєтеся робити з проблемою Грю? Ти просто залишиш мене тут?"

# game/tutorial_quickstart.rpy:188
translate ukrainian tutorial_dialogue_023bcd31:

    # e "Here's the full game, including the two new lines of dialogue, both of which use the Character we defined to say dialogue."
    e "Ось повна гра, включно з двома новими діалоговими рядками, обидві з яких використовують Character, якого ми визначили, щоб говорити в діалозі."

# game/tutorial_quickstart.rpy:190
translate ukrainian tutorial_dialogue_48bb9547:

    # e "The one-argument form of the say statement is unchanged, but in the two-argument form, instead of the first string we can use a short name."
    e "Форма оператора say з одним аргументом не змінюється, але у формі з двома аргументами замість першого рядка ми можемо використовувати коротке ім’я."

# game/tutorial_quickstart.rpy:192
translate ukrainian tutorial_dialogue_56a9936f:

    # e "When this say statement is run, Ren'Py will look up the short name, which is really a Python variable. It will then use the associated Character to show the dialogue."
    e "Коли виконується цей оператор say, Ren'Py шукатиме коротку назву, яка насправді є змінною Python. Потім він використовуватиме пов’язаний Character для показу діалогу."

# game/tutorial_quickstart.rpy:194
translate ukrainian tutorial_dialogue_d5984a21:

    # e "The Character object controls who is speaking, the color of their name, and many other properties of the dialogue."
    e "Об’єкт 'Character' керує тим, хто говорить, кольором його імені та багатьма іншими властивостями діалогу."

# game/tutorial_quickstart.rpy:198
translate ukrainian tutorial_dialogue_a5bcac8b:

    # e "Since the bulk of a visual novel is dialogue, we've tried to make it as easy to write as possible."
    e "Оскільки основну частину візуального роману становлять діалоги, ми намагалися зробити його написання максимально легким."

# game/tutorial_quickstart.rpy:200
translate ukrainian tutorial_dialogue_6b9a42d0:

    # e "Hopefully, by allowing the use of short names for characters, we've succeeded."
    e "Сподіваємося, дозволивши використовувати короткі імена для персонажів, ми досягнемо успіху."

# game/tutorial_quickstart.rpy:206
translate ukrainian tutorial_images_e09ac970:

    # e "A visual novel isn't much without images. So let's add some images to our little game."
    e "Візуальний роман не обходиться без зображень. Отже, давайте додамо кілька зображень до нашої маленької гри."

# game/tutorial_quickstart.rpy:208
translate ukrainian tutorial_images_40140793:

    # e "Before we can show images, we must first choose image names, then place the image files into the images directory."
    e "Перш ніж ми зможемо показати зображення, ми повинні спочатку вибрати назви зображень, а потім розмістити файли зображень у папці images."

# game/tutorial_quickstart.rpy:210
translate ukrainian tutorial_images_d73388f8:

    # e "An image name is something like 'bg cave' or 'lucy happy', with one or more parts separated by spaces."
    e "Ім’я зображення - це щось на кшталт 'bg cave' або 'lucy happy', одна або кілька частин розділені пробілами."

# game/tutorial_quickstart.rpy:212
translate ukrainian tutorial_images_2d5596d4:

    # e "Each part should start with a lower-case letter, and then contain lower-case letters, numbers, and underscores."
    e "Кожна частина має починатися з малої літери, а потім може містити малі літери, цифри та підкреслення."

# game/tutorial_quickstart.rpy:214
translate ukrainian tutorial_images_e02c0c82:

    # e "The first part of an image is called the tag. For 'bg cave' the tag is 'bg', while for 'lucy happy' the tag is 'lucy'."
    e "Перша частина зображення називається тегом. Для 'bg cave' тег — 'bg', а для 'lucy happy' — 'lucy'."

# game/tutorial_quickstart.rpy:216
translate ukrainian tutorial_images_d5eafcf2:

    # e "You can open the images directory by clicking the appropriate button in the Ren'Py launcher."
    e "Ви можете відкрити папку images, натиснувши відповідну кнопку в лаунчері Ren'Py."

# game/tutorial_quickstart.rpy:218
translate ukrainian tutorial_images_e4b12fb6:

    # e "The files in the images directory should have the same name as the image, followed by an extension like .jpg, .png, or .webp."
    e "Файли в папці images повинні мати таке ж ім’я, як і зображення, а потім розширення, наприклад .jpg, .png або .webp."

# game/tutorial_quickstart.rpy:220
translate ukrainian tutorial_images_a3bd89b2:

    # e "Our example uses 'bg cave.jpg', 'lucy happy.png', and 'lucy mad.png'."
    e "У нашому прикладі використовуються 'bg cave.jpg', 'lucy happy.png' і 'lucy mad.png'."

# game/tutorial_quickstart.rpy:224
translate ukrainian tutorial_images_76b954de:

    # e "Let's see what those look like in the game."
    e "Давайте подивимося, як вони виглядають у грі."

# game/tutorial_quickstart.rpy:230
translate ukrainian tutorial_images_f04e72ea:

    # l "Now that the lights are on, we don't have to worry about Grues anymore."
    l "Тепер, коли світло ввімкнуто, нам більше не потрібно турбуватися про Грюса."

# game/tutorial_quickstart.rpy:234
translate ukrainian tutorial_images_d77ffa1c:

    # l "But what's the deal with me being in a cave? Eileen gets to be out in the sun, and I'm stuck here!"
    l "Але яка справа в тому, що я в печері? Ейлін вийшла на сонце, а я тут застрягла!"

# game/tutorial_quickstart.rpy:242
translate ukrainian tutorial_images_6c0c938b:

    # e "Here's the script for that scene. Notice how it includes two new statements, the scene and show statement."
    e "Ось скрипт цієї сцени. Зверніть увагу, що він містить два нових оператори, оператор scene та show."

# game/tutorial_quickstart.rpy:244
translate ukrainian tutorial_images_1a4660b9:

    # e "The scene statement clears the screen, and then adds a background image."
    e "Оператор scene очищає екран, а потім додає фонове зображення."

# game/tutorial_quickstart.rpy:246
translate ukrainian tutorial_images_a5e7eb24:

    # e "The show statement adds a new image on top of all the other images on the screen."
    e "Оператор show додає нове зображення поверх усіх інших зображень на екрані."

# game/tutorial_quickstart.rpy:248
translate ukrainian tutorial_images_86e11de2:

    # e "If there is already an image with the same tag, the new image is used to replace the old one."
    e "Якщо вже є зображення з таким тегом, нове зображення використовується замість старого."

# game/tutorial_quickstart.rpy:250
translate ukrainian tutorial_images_802825f2:

    # e "Changes to the list of shown images take place instantly, so in the example, the user won't see the background by itself."
    e "Зміни в списку показаних зображень відбуваються миттєво, тому в прикладі користувач не побачить сам фон."

# game/tutorial_quickstart.rpy:252
translate ukrainian tutorial_images_b246dfdd:

    # e "The second show statement has an at clause, which gives a location on the screen. Common locations are left, right, and center, but you can define many more."
    e "Другий оператор show має властивість at, яка вказує розташування на екрані. Типовими розташуваннями є left, right і center, але ви можете визначити багато інших."

# game/tutorial_quickstart.rpy:257
translate ukrainian tutorial_images_82fceeb8:

    # e "In this example, we show an image named logo base, and we show it at a creator-defined position, rightish."
    e "У цьому прикладі ми показуємо зображення під назвою logo base, і ми показуємо його в положенні, визначеному автором, праворуч."

# game/tutorial_quickstart.rpy:259
translate ukrainian tutorial_images_9defda43:

    # e "We also specify that it should be shown behind another image, in this case eileen. That's me."
    e "Ми також уточнюємо, що воно має бути показано за іншим зображенням, у даному випадку Ейлін. Це я."

# game/tutorial_quickstart.rpy:264
translate ukrainian tutorial_images_73d331f7:

    # e "Finally, there's the hide statement, which hides the image with the given tag."
    e "І нарешті, є оператор hide, який приховує зображення з заданим тегом."

# game/tutorial_quickstart.rpy:266
translate ukrainian tutorial_images_f34f62d5:

    # e "Since the show statement replaces an image, and the scene statement clears the scene, it's pretty rare to hide an image."
    e "Оскільки оператор show замінює зображення, а оператор scene очищає сцену, досить рідко можна приховати зображення."

# game/tutorial_quickstart.rpy:268
translate ukrainian tutorial_images_e06fa53a:

    # e "The main use is for when a character or prop leaves before the scene is over."
    e "Основне використання, коли персонаж або реквізит йде до завершення сцени."

# game/tutorial_quickstart.rpy:282
translate ukrainian tutorial_simple_positions_b492e793:

    # e "When the standard positions that come with Ren'Py aren't enough for you, you can create your own. Here, I'll show you the easy way to do it."
    e "Якщо стандартних позицій, які постачаються з Ren'Py, вам недостатньо, ви можете створити власні. Ось я покажу вам простий спосіб зробити це."

# game/tutorial_quickstart.rpy:291
translate ukrainian tutorial_simple_positions_04e3bc44:

    # e "The first way to do it is to show an image followed by a colon. Then indented on the next couple of lines are the xalign and yalign transform properties."
    e "Перший спосіб зробити це — показати зображення, після якого стоїть двокрапка. Потім у наступній парі рядків з відступом є властивості трансформації xalign і yalign."

# game/tutorial_quickstart.rpy:293
translate ukrainian tutorial_simple_positions_3ecad5f8:

    # e "Each of the transform properties is a name followed by a value. For xalign and yalign, the values are numbers."
    e "Кожна властивість трансформації — це ім’я, за яким слідує значення. Для xalign і yalign значеннями є числа."

# game/tutorial_quickstart.rpy:295
translate ukrainian tutorial_simple_positions_61c1b124:

    # e "The xalign transform property is the important one, as it controls where the image is placed horizontally on the screen."
    e "Властивість трансформації xalign є важливою, оскільки вона контролює горизонтальне розташування зображення на екрані."

# game/tutorial_quickstart.rpy:305
translate ukrainian tutorial_simple_positions_67ebea97:

    # e "An xalign of 0.0 is the left side."
    e "Xalign 0,0 - ліва сторона."

# game/tutorial_quickstart.rpy:315
translate ukrainian tutorial_simple_positions_bd4f56d8:

    # e "0.5 is the center."
    e "0,5 - центр."

# game/tutorial_quickstart.rpy:324
translate ukrainian tutorial_simple_positions_fb2c48f2:

    # e "And 1.0 is the right. The decimal place is important and has to be there. Just 1 by itself won't work the same."
    e "І 1.0 - права сторона. Десятковий знак важливий і повинен бути там. Лише 1 сам по собі не працюватиме однаково."

# game/tutorial_quickstart.rpy:333
translate ukrainian tutorial_simple_positions_8eebc9a7:

    # e "Of course, you can pick any position in between."
    e "Звичайно, ви можете вибрати будь-яку проміжну позицію."

# game/tutorial_quickstart.rpy:335
translate ukrainian tutorial_simple_positions_4cd917f6:

    # e "The yalign property is the same way, with 0.0 being the top of the screen and 1.0 being the bottom. Since most sprites stick to the bottom, it's almost always 1.0."
    e "Властивість yalign працює так само, де 0.0 - верхня частина екрана, а 1.0 - нижня. Оскільки більшість спрайтів прилипають донизу, це майже завжди 1,0."

# game/tutorial_quickstart.rpy:341
translate ukrainian tutorial_simple_positions_fbd1a3eb:

    # e "While being able to write positions like this is useful, having to repeatedly do so isn't. So Ren'Py lets you define a transform once, and reuse it."
    e "Хоча можливість писати подібні позиції є корисною, потреба робити це неодноразово - ні. Отже, Ren'Py дозволяє вам визначити трансформацію один раз і використовувати його повторно."

# game/tutorial_quickstart.rpy:345
translate ukrainian tutorial_simple_positions_2377e3b3:

    # e "Usually transforms are defined at the top of a file, right after the characters. But it doesn't matter to Ren'Py where you define them."
    e "Зазвичай трансформацію визначають у верхній частині файлу, одразу після символів. Але для Ren'Py не має значення, де ви їх визначаєте."

# game/tutorial_quickstart.rpy:347
translate ukrainian tutorial_simple_positions_3ce7e367:

    # e "The transform is given a name, slightleft, and then the xalign and yalign properties."
    e "Трансформація отримує назву, slightleft, а потім властивості xalign і yalign."

# game/tutorial_quickstart.rpy:355
translate ukrainian tutorial_simple_positions_82d640d9:

    # e "Once a transform has been defined, you can use it in the at clause of the show statement."
    e "Після визначення трансформації ви можете використовувати його в властивості at оператора show."

# game/tutorial_quickstart.rpy:360
translate ukrainian tutorial_simple_positions_16b66785:

    # e "Transforms are sticky. If you replace an image without using a transform, Ren'Py will keep the same transforms it had been using."
    e "Трансформації липкі. Якщо ви заміните зображення без використання трансформації, Ren'Py збереже ті самі трансформації, які він використовував."

# game/tutorial_quickstart.rpy:364
translate ukrainian tutorial_simple_positions_5d5e0cfd:

    # e "Of course, there's a lot more to transforms than this. If you want to learn more, you can read the sections on Position Properties, Transforms and Animation, and Transform Properties."
    e "Звісно, є набагато більше трансформацій, ніж це. Якщо ви хочете дізнатися більше, ви можете прочитати розділи 'Властивості позиції', 'Трансформації та анімація' та 'Властивості трансформації'."

# game/tutorial_quickstart.rpy:366
translate ukrainian tutorial_simple_positions_e65da9bf:

    # e "But for many visual novels, xalign and yalign are the only properties that matter."
    e "Але для багатьох візуальних романів xalign і yalign є єдиними властивостями, які мають значення."

# game/tutorial_quickstart.rpy:381
translate ukrainian tutorial_transitions_9b8c714c:

    # e "It can be somewhat jarring for the game to jump from place to place."
    e "Переміщатися з місця на місце для гри може бути дещо неприємно."

# game/tutorial_quickstart.rpy:388
translate ukrainian tutorial_transitions_3e290ea8:

    # e "To help take some of edge off a change in scene, Ren'Py supports the use of transitions. Let's try that scene change again, but this time we'll use transitions."
    e "Щоб полегшити зміну сцени, Ren'Py підтримує використання переходів. Давайте знову спробуємо змінити сцену, але цього разу ми використаємо переходи."

# game/tutorial_quickstart.rpy:402
translate ukrainian tutorial_transitions_9c0a86c4:

    # e "That's much smoother. Here's some example code showing how we include transitions in our game."
    e "Це набагато плавніше. Ось приклад коду, який показує, як ми включаємо переходи в нашу гру."

# game/tutorial_quickstart.rpy:404
translate ukrainian tutorial_transitions_3e490d40:

    # e "It uses the with statement. The with statement causes the scene to transition from the last things shown to the things currently being shown."
    e "Він використовує оператор with. Оператор with змушує сцену переходити від останніх показаних речей до речей, які показуються в даний момент."

# game/tutorial_quickstart.rpy:406
translate ukrainian tutorial_transitions_a43847df:

    # e "It takes a transition as an argument. In this case, we're using the Dissolve transition. This transition takes as an argument the amount of time the dissolve should take."
    e "Він приймає перехід як аргумент. У цьому випадку ми використовуємо перехід 'Dissolve'. Цей перехід приймає як аргумент кількість часу, який має зайняти dissolve."

# game/tutorial_quickstart.rpy:408
translate ukrainian tutorial_transitions_6fcee414:

    # e "In this case, each transition takes half a second."
    e "У цьому випадку кожен перехід займає півсекунди."

# game/tutorial_quickstart.rpy:412
translate ukrainian tutorial_transitions_033042cc:

    # e "We can define a short name for a transition, using the define statement. Here, we're defining slowdissolve to be a dissolve that takes a whole second."
    e "Ми можемо визначити коротку назву для переходу, використовуючи оператор define. Тут ми визначаємо slowdissolve як dissolve, яке займає цілу секунду."

# game/tutorial_quickstart.rpy:427
translate ukrainian tutorial_transitions_0ba82f00:

    # e "Once a transition has been given a short name, we can use it in our game."
    e "Якщо переходу присвоєно коротку назву, ми можемо використовувати його в нашій грі."

# game/tutorial_quickstart.rpy:431
translate ukrainian tutorial_transitions_51ff9600:

    # e "Ren'Py defines some transitions for you, like dissolve, fade, and move. For more complex or customized transitions, you'll have to define your own."
    e "Ren'Py визначає деякі переходи для вас, як-от dissolve, fade та move. Для більш складних або індивідуальних переходів вам доведеться визначити власні."

# game/tutorial_quickstart.rpy:433
translate ukrainian tutorial_transitions_1528f73f:

    # e "If you're interested, check out the Transitions Gallery section of this tutorial."
    e "Якщо ви зацікавлені, перегляньте розділ 'Переходи' в цьому посібнику."

# game/tutorial_quickstart.rpy:439
translate ukrainian tutorial_music_8b92efb7:

    # e "Another important part of a visual novel or simulation game is the soundtrack."
    e "Іншою важливою частиною візуального роману чи гри-симулятора є звуковий супровід."

# game/tutorial_quickstart.rpy:441
translate ukrainian tutorial_music_53910317:

    # e "Ren'Py breaks sound up into channels. The channel a sound is played on determines if the sound loops, and if it is saved and restored with the game."
    e "Ren'Py розбиває звук на канали. Канал, на якому відтворюється звук, визначає, чи повторюється звук, а також чи зберігається та відновлюється під час гри."

# game/tutorial_quickstart.rpy:443
translate ukrainian tutorial_music_a1e37712:

    # e "When a sound is played on the music channel, it is looped, and it is saved when the game is saved."
    e "Коли звук відтворюється на музичному каналі, він зациклюється та зберігається під час збереження гри."

# game/tutorial_quickstart.rpy:445
translate ukrainian tutorial_music_d9086d22:

    # e "When the channel named sound is used, the sound is played once and then stopped. It isn't saved."
    e "Коли використовується канал під назвою sound, звук відтворюється один раз, а потім припиняється. Не зберігається."

# game/tutorial_quickstart.rpy:447
translate ukrainian tutorial_music_3555b640:

    # e "The sounds themselves are stored in audio files. Ren'Py supports the Opus, Ogg Vorbis, and mp3 formats."
    e "Самі звуки зберігаються в аудіофайлах. Ren'Py підтримує формати Opus, Ogg Vorbis і mp3."

# game/tutorial_quickstart.rpy:449
translate ukrainian tutorial_music_a2ffbe9b:

    # e "Let's check out some of the commands that can affect the music channel."
    e "Давайте перевіримо деякі команди, які можуть впливати на музичний канал."

# game/tutorial_quickstart.rpy:454
translate ukrainian tutorial_music_8b606a55:

    # e "The play music command replaces the currently playing music, and replaces it with the named filename."
    e "Команда 'play music' замінює музику, яка зараз відтворюється, і замінює її з назви на назву файлу."

# game/tutorial_quickstart.rpy:456
translate ukrainian tutorial_music_18650fe7:

    # e "If you specify the currently-playing song, it will restart it."
    e "Якщо ви вкажете пісню, яка зараз відтворюється, її буде перезапущено."

# game/tutorial_quickstart.rpy:458
translate ukrainian tutorial_music_413d91fc:

    # e "If the optional fadeout clause is given, it will fade out the currently playing music before starting the new music."
    e "Якщо вказано необов’язкове fadeout, це призведе до згасання поточної музики перед початком нової музики."

# game/tutorial_quickstart.rpy:463
translate ukrainian tutorial_music_a282a0e3:

    # e "The queue statement also adds music to the named channel, but it waits until the currently-playing song is finished before playing the new music."
    e "Оператор queue також додає музику до вказаного каналу, але перед відтворенням нової музики він очікує, доки завершиться пісня, яка зараз відтворюється."

# game/tutorial_quickstart.rpy:468
translate ukrainian tutorial_music_01ca6bad:

    # e "The third statement is the stop statement. It stops the music playing on a channel. It too takes the fadeout clause."
    e "Третій оператор - це оператор stop. Він зупиняє відтворення музики на каналі. Це також вимагає fadeout."

# game/tutorial_quickstart.rpy:473
translate ukrainian tutorial_music_384937da:

    # e "Unlike the music channel, playing a sound on the sound channel causes it to play only once."
    e "На відміну від музичного каналу, відтворення звуку на звуковому каналі спричиняє його відтворення лише один раз."

# game/tutorial_quickstart.rpy:480
translate ukrainian tutorial_music_1d3e9fd2:

    # e "You can queue up multiple sounds on the sound channel, but the sounds will only play one at a time."
    e "Ви можете поставити в чергу кілька звуків у звуковому каналі, але звуки відтворюватимуться лише по одному."

# game/tutorial_quickstart.rpy:486
translate ukrainian tutorial_music_aa01c19d:

    # e "Ren'Py has separate mixers for sound, music, and voices, so the player can adjust them as they like."
    e "У Ren'Py є окремі мікшери для звуку, музики та голосів, тому гравець може налаштовувати їх на свій розсуд."

# game/tutorial_quickstart.rpy:492
translate ukrainian tutorial_menus_0426904b:

    # e "Many visual novels require the player to make choices from in-game menus. These choices can add some challenge to the game, or adjust it to the player's preferences."
    e "Багато візуальних романів вимагають від гравця вибору в ігровому меню. Ці варіанти можуть ускладнити гру або налаштувати її відповідно до вподобань гравця."

# game/tutorial_quickstart.rpy:494
translate ukrainian tutorial_menus_431eeff0:

    # e "Do you like to play visual novels with choices in them?"
    e "Ви любите грати у візуальні романи з вибором у них?"

# game/tutorial_quickstart.rpy:509
translate ukrainian choice1_yes_f6d95df8:

    # e "While creating a multi-path visual novel can be a bit more work, it can yield a unique experience."
    e "Незважаючи на те, що створення багатопланового візуального роману може зайняти трохи більше праці, воно може дати унікальний досвід."

# game/tutorial_quickstart.rpy:517
translate ukrainian choice1_no_72958b50:

    # e "Games without menus are called kinetic novels, and there are dozens of them available to play."
    e "Ігри без меню називаються кінетичними романами, і в них доступні десятки."

# game/tutorial_quickstart.rpy:528
translate ukrainian choice1_done_acba9504:

    # e "Here, you can see the code for that menu. If you scroll down, you can see the code we run after the menu."
    e "Тут ви можете побачити код цього меню. Якщо ви прокрутите вниз, ви побачите код, який ми запускаємо після меню."

# game/tutorial_quickstart.rpy:530
translate ukrainian choice1_done_f9fa6889:

    # e "Menus are introduced by the menu statement. The menu statement takes an indented block, in which there can be one line of dialogue and multiple choices."
    e "Меню вводяться оператором menu. Оператор menu займає блок із відступом, у якому може бути один рядок діалогу та декілька варіантів вибору."

# game/tutorial_quickstart.rpy:532
translate ukrainian choice1_done_ebb2db38:

    # e "Each choice must end with a colon, as each choice has its own block of Ren'Py code, that is run when that choice is selected."
    e "Кожен вибір має закінчуватися двокрапкою, оскільки кожен вибір має власний блок коду Ren'Py, який запускається, коли цей вибір вибрано."

# game/tutorial_quickstart.rpy:534
translate ukrainian choice1_done_59cac95d:

    # e "Here, each block jumps to a label. While you could put small amounts of Ren'Py code inside a menu label, it's probably good practice to usually jump to a bigger block of code."
    e "Тут кожен блок переходить до мітки. Хоча ви можете розміщувати невелику кількість коду Ren'Py у мітці меню, мабуть, це гарна практика, як правило, переходити до більшого блоку коду."

# game/tutorial_quickstart.rpy:536
translate ukrainian choice1_done_2851a313:

    # e "Scrolling down past the menu, you can see the labels that the menu jumps to. There are three labels here, named choice1_yes, choice1_no, and choice1_done."
    e "Прокручуючи меню вниз, ви можете побачити мітки, до яких переходить меню. Тут є три мітки з назвами choice1_yes, choice1_no, та choice1_done."

# game/tutorial_quickstart.rpy:538
translate ukrainian choice1_done_ff761b03:

    # e "When the first menu choice is picked, we jump to the choice1_yes, which runs two lines of script before jumping to choice1_done."
    e "Коли вибрано перший пункт меню, ми переходимо до choice1_yes, який виконує два рядки скрипту перед переходом до choice1_done."

# game/tutorial_quickstart.rpy:540
translate ukrainian choice1_done_664fe702:

    # e "Similarly, picking the second choice jumps us to choice1_no, which also runs two lines of script."
    e "Подібним чином вибір другого варіанту переходимо на choice1_no, який також запускає два рядки скрипту."

# game/tutorial_quickstart.rpy:542
translate ukrainian choice1_done_31d12b1e:

    # e "The lines beginning with the dollar sign are lines of python code, which are used to set a flag based on the user's choice."
    e "Рядки, що починаються зі знаком долара, є рядками коду Python, які використовуються для встановлення прапора на основі вибору користувача."

# game/tutorial_quickstart.rpy:544
translate ukrainian choice1_done_88398d3e:

    # e "The flag is named menu_flag, and it's set to True or False based on the user's choice. The if statement can be used to test a flag, so the game can remember the user's choices."
    e "Прапорець називається menu_flag і встановлюється на True або False залежно від вибору користувача. Оператор if можна використовувати для перевірки прапора, щоб гра могла запам’ятати вибір користувача."

# game/tutorial_quickstart.rpy:549
translate ukrainian choice1_done_2828dbfc:

    # e "For example, I remember that you plan to use menus in your game."
    e "Наприклад, я пам’ятаю, що ви плануєте використовувати меню у своїй грі."

# game/tutorial_quickstart.rpy:553
translate ukrainian choice1_done_503786e4:

    # e "For example, I remember that you're planning to make a kinetic novel, without menus."
    e "Наприклад, я пам’ятаю, що ви плануєте зробити кінетичний роман, без меню."

# game/tutorial_quickstart.rpy:555
translate ukrainian choice1_done_819e234a:

    # e "Here's an example that shows how we can test a flag, and do different things if it is true or not."
    e "Ось приклад, який показує, як ми можемо перевірити прапор і робити різні речі, правдивий він чи ні."

# game/tutorial_quickstart.rpy:560
translate ukrainian choice1_done_461e6a59:

    # e "Finally, this shows how you can show dialogue and menus at the same time. Understand?" nointeract
    e "І нарешті, це показує, як ви можете показувати діалог і меню одночасно. Зрозуміло?" nointeract

# game/tutorial_quickstart.rpy:564
translate ukrainian choice1_done_a32e30fd:

    # e "Great."
    e "Чудово."

# game/tutorial_quickstart.rpy:568
translate ukrainian choice1_done_fbd1dbc1:

    # e "If you look at the example, before the first choice, there's an indented say statement."
    e "Якщо ви подивитися на приклад, перед першим вибором є оператор say із відступом."

# game/tutorial_quickstart.rpy:574
translate ukrainian menu3_done_47fa2268:

    # e "Although we won't demonstrate it here, Ren'Py supports making decisions based on a combinations of points, flags, and other factors."
    e "Хоча ми не будемо демонструвати це тут, Ren'Py підтримує прийняття рішень на основі комбінацій точок, прапорів та інших факторів."

# game/tutorial_quickstart.rpy:576
translate ukrainian menu3_done_826a600b:

    # e "One of Ren'Py's big advantages is the flexibility using a scripting language like Python provides us. It lets us easily scale from kinetic novels to complex simulation games."
    e "Однією з великих переваг Ren'Py є гнучкість використання такої мови скриптів, як Python. Це дозволяє нам легко переходити від кінетичних романів до складних ігор-симуляторів."

# game/tutorial_quickstart.rpy:585
translate ukrainian tutorial_input_066611c5:

    # e "Some games might prompt the player for input."
    e "Деякі ігри можуть запропонувати гравцеві ввести дані."

# game/tutorial_quickstart.rpy:599
translate ukrainian tutorial_input_dc3b4560:

    # e "That's done with Python, and especially the renpy.input function. The first line of this example prompts the player for some texts, and sticks it in the name variable."
    e "Це зроблено за допомогою Python, особливо функції renpy.input. Перший рядок цього прикладу запитує у гравця деякі тексти та вставляє їх у змінну імені."

# game/tutorial_quickstart.rpy:601
translate ukrainian tutorial_input_c88b3f4e:

    # e "Often times, you'll want to clean the name up before you use it. The last line does that, by calling the strip method to remove whitespace, and replacing the name with a default if it's missing."
    e "Часто вам потрібно очистити назву, перш ніж використовувати її. Останній рядок робить це шляхом виклику методу strip для видалення пробілів і заміни імені на типове, якщо воно відсутнє."

# game/tutorial_quickstart.rpy:605
translate ukrainian tutorial_input_1236e9da:

    # e "To interpolate a variable, write it in square brackets. Isn't that right, [name]?"
    e "Щоб інтерполювати змінну, запишіть її в квадратних дужках. Чи не так, [name]?"

# game/tutorial_quickstart.rpy:609
translate ukrainian tutorial_input_c1f7a808:

    # e "Variable names can also be shown in character names. To do that, just include the variable in square brackets in the character's name. Got it?"
    e "Імена змінних також можуть відображатися в назвах персонажів. Для цього просто додайте змінну в квадратних дужках до імені персонажа. Зрозуміло?"

# game/tutorial_quickstart.rpy:612
translate ukrainian tutorial_input_f7757a8e:

    # g "I think I do."
    g "Я думаю, що так."

# game/tutorial_quickstart.rpy:619
translate ukrainian tutorial_input_0548d3e2:

    # e "Variable interpolation also works with other variables. Here, the answer is [answer] and the flag is [flag]."
    e "Інтерполяція змінних також працює з іншими змінними. Тут відповідь - [answer], а прапор - [flag]."

translate ukrainian strings:

    # game/tutorial_quickstart.rpy:2
    old "Lucy"
    new "Люсі"

    # game/tutorial_quickstart.rpy:497
    old "Yes, I do."
    new "Так."

    # game/tutorial_quickstart.rpy:497
    old "No, I don't."
    new "Ні."

    # game/tutorial_quickstart.rpy:589
    old "What's your name?"
    new "Як вас звати?"

    # game/tutorial_quickstart.rpy:591
    old "Guy Shy"
    new "Сором'язливий хлопець"

