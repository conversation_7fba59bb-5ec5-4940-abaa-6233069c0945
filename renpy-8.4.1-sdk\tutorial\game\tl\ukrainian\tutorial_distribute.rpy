﻿
# game/tutorial_distribute.rpy:3
translate ukrainian distribute_7db9b042:

    # e "One thing Ren'<PERSON><PERSON> makes easy is building distributions of your visual novel so you can give it to players."
    e "Ren'Py полегшує створення дистрибутивів вашого візуального роману, щоб ви могли надати його гравцям."

# game/tutorial_distribute.rpy:5
translate ukrainian distribute_d373903b:

    # e "Before you build distributions, you should use the Lint command to check your game for problems."
    e "Перш ніж створювати дистрибутиви, вам слід скористатися командою Lint, щоб перевірити гру на наявність проблем."

# game/tutorial_distribute.rpy:7
translate ukrainian distribute_2f970565:

    # e "While not every potential problem lint reports is a real issue, they generally are, and you should try to understand what might be wrong."
    e "Хоча не всі повідомлення про потенційну проблему є справжньою проблемою, зазвичай це так, і вам слід спробувати зрозуміти, що може бути не так."

# game/tutorial_distribute.rpy:12
translate ukrainian distribute_29aea017:

    # e "After lint has finished, you can choose Build Distributions to build the Windows, Linux, and Mac distributions of your game."
    e "Після того, як lint закінчиться, ви можете вибрати 'Створити дистрибутиви', щоб створити дистрибутиви вашої гри для Windows, Linux і Mac."

# game/tutorial_distribute.rpy:14
translate ukrainian distribute_821be97b:

    # e "This can be as simple as clicking the Build button, when you're not on a Mac."
    e "Це може бути так само просто, як натиснути кнопку 'Створити', коли ви не на Mac."

# game/tutorial_distribute.rpy:16
translate ukrainian distribute_638f964a:

    # e "If you are on a Macintosh, you can have Ren'Py sign the Mac application, which makes it easier for players to run. To enable this, you need to set build.mac_identity in options.rpy."
    e "Якщо ви користуєтеся Macintosh, ви можете попросити Ren'Py підписати програму Mac, що полегшить роботу гравців. Щоб увімкнути це, вам потрібно встановити build.mac_identity в options.rpy."

# game/tutorial_distribute.rpy:21
translate ukrainian distribute_dd1af4dd:

    # e "Ren'Py supports the mobile platforms, Android and iOS. We also support ChromeOS, through its ability to run Android apps."
    e "Ren'Py підтримує мобільні платформи Android та iOS. Ми також підтримуємо ОС Chrome завдяки її здатності запускати програми Android."

# game/tutorial_distribute.rpy:23
translate ukrainian distribute_0b547b7b:

    # e "These mobile platforms can be a bit more complicated. While Android apps can be built everywhere, iOS requires a Mac."
    e "Ці мобільні платформи можуть бути дещо складнішими. Хоча програми для Android можна створювати скрізь, для iOS потрібен Mac."

# game/tutorial_distribute.rpy:25
translate ukrainian distribute_50a57bcf:

    # e "Mobile platforms might also require you to change your visual novel a little, due to the smaller limited devices. For example, buttons need to be made large enough for a person to touch."
    e "Мобільні платформи також можуть вимагати від вас дещо змінити свій візуальний роман через менші обмежені пристрої. Наприклад, ґудзики потрібно зробити такими великими, щоб людина їх торкалася."

# game/tutorial_distribute.rpy:27
translate ukrainian distribute_a9a2149f:

    # e "Rather than cover mobile here, I'll point you to the {a=https://www.renpy.org/doc/html/android.html}Android{/a} and {a=https://www.renpy.org/doc/html/ios.html}iOS{/a} documentation, where you can read more."
    e "Замість того, щоб розглядати тут мобільні пристрої, я вкажу вам на {a=https://www.renpy.org/doc/html/android.html}Android{/a} і {a=https://www.renpy .org/doc/html/ios.html}iOS{/a}, де ви можете прочитати більше."

# game/tutorial_distribute.rpy:29
translate ukrainian distribute_f1cbc9d0:

    # e "Thanks to the distribution tools Ren'Py ships with, there are thousands of visual novels available."
    e "Завдяки інструментам розповсюдження Ren'Py доступні тисячі візуальних романів."

# game/tutorial_distribute.rpy:33
translate ukrainian distribute_500b3e7f:

    # e "I hope that soon, yours will be one of them!"
    e "Сподіваюся, незабаром і ваш стане одним із них!"

