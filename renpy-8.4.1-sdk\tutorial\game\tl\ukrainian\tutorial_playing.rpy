﻿
# game/tutorial_playing.rpy:11
translate ukrainian tutorial_playing_2985ab86:

    # e "As someone who has played more than a few visual novels, there are many features that I expect all games to have."
    e "Як людина, яка грала більше ніж у кілька візуальних романів, я очікую, що всі ігри мають багато функцій."

# game/tutorial_playing.rpy:13
translate ukrainian tutorial_playing_ca4769bb:

    # e "Features like saving, loading, changing preferences, and so on."
    e "Такі функції, як збереження, завантаження, зміна налаштувань тощо."

# game/tutorial_playing.rpy:15
translate ukrainian tutorial_playing_f30f1979:

    # e "One of the nice things about Ren'Py is that the engine provides many of these features for you. You can spend your time creating your game, and let us provide these things."
    e "Однією з приємних речей Ren'Py є те, що механізм надає багато з цих функцій для вас. Ви можете витратити свій час на створення своєї гри, і дозвольте нам надати ці речі."

# game/tutorial_playing.rpy:17
translate ukrainian tutorial_playing_afa743e7:

    # e "While you're in the game, you can access the game menu by right clicking or hitting the escape key."
    e "Перебуваючи в грі, ви можете отримати доступ до меню гри, клацнувши правою кнопкою миші або натиснувши клавішу 'escape'."

# game/tutorial_playing.rpy:19
translate ukrainian tutorial_playing_1a6c8296:

    # e "You can also access the game menu through some of the quick menu buttons at the bottom of this screen."
    e "Ви також можете отримати доступ до меню гри за допомогою деяких кнопок швидкого меню внизу цього екрана."

# game/tutorial_playing.rpy:25
translate ukrainian tutorial_playing_8360224a:

    # e "When you first enter the game menu, you'll see the save screen. Clicking on a numbered slot will save the game."
    e "Коли ви вперше входите в меню гри, ви побачите екран збереження. Натискання на пронумерований слот збереже гру."

# game/tutorial_playing.rpy:27
translate ukrainian tutorial_playing_edea14ff:

    # e "Unlike other engines, Ren'Py doesn't limit the number of save slots that you can use. You can keep hitting next until you reach the page you want."
    e "На відміну від інших двигунів, Ren'Py не обмежує кількість слотів збереження, які ви можете використовувати. Ви можете продовжувати натискати далі, доки не дійдете до потрібної сторінки."

# game/tutorial_playing.rpy:29
translate ukrainian tutorial_playing_900ce396:

    # e "Clicking on the title of the page allows you to start typing to change the page name."
    e "Натиснувши на заголовок сторінки, ви зможете почати вводити текст, щоб змінити назву сторінки."

# game/tutorial_playing.rpy:31
translate ukrainian tutorial_playing_fea05c6b:

    # e "The load screen looks quite similar to the save screen, and lets you load a game from a save slot."
    e "Екран завантаження виглядає дуже схожим на екран збереження, і дозволяє завантажити гру зі слота збереження."

# game/tutorial_playing.rpy:33
translate ukrainian tutorial_playing_8e7e83a8:

    # e "It also lets you load one of the auto-saves that Ren'Py makes for you."
    e "Це також дозволяє завантажити одне з автоматичних збережень, які Ren'Py робить для вас."

# game/tutorial_playing.rpy:38
translate ukrainian tutorial_playing_4b21f071:

    # e "The game menu also has the preferences screen."
    e "У меню гри також є екран налаштувань."

# game/tutorial_playing.rpy:40
translate ukrainian tutorial_playing_eaac8ba9:

    # e "This screen lets you decide how Ren'Py displays, pick what Ren'Py skips, control text speed and auto-click speed, and adjust sound, music, and voice volumes."
    e "Цей екран дозволяє вирішувати, як Ren'Py відображатиметься, вибирати, що Ren'Py пропускає, керувати швидкістю тексту та швидкістю авточитання, а також регулювати гучність звуку, музики та голосу."

# game/tutorial_playing.rpy:42
translate ukrainian tutorial_playing_b1562a34:

    # e "The game menu also lets you end the game and return to the main menu, or quit Ren'Py entirely."
    e "Меню гри також дозволяє завершити гру та повернутися до головного меню або повністю вийти з Ren'Py."

# game/tutorial_playing.rpy:47
translate ukrainian tutorial_playing_790f9dc7:

    # e "While the default game menus look a bit generic, with a little work they can be customized or even entirely replaced, allowing you to create menus as unique as your game."
    e "Хоча меню гри за замовчуванням виглядає дещо загальним, з невеликою роботою їх можна налаштувати або навіть повністю замінити, дозволяючи створювати меню, унікальні, як ваша гра."

# game/tutorial_playing.rpy:53
translate ukrainian tutorial_playing_bc29822e:

    # e "While inside the game, there are a few more things you can do."
    e "У грі ви можете зробити ще кілька речей."

# game/tutorial_playing.rpy:55
translate ukrainian tutorial_playing_dc0f9cf7:

    # e "When I'm liking a visual novel, I want to see all the endings. Ren'Py's skip function lets me easily do this, by skipping text that I've already seen."
    e "Коли мені подобається візуальний роман, я хочу бачити всі кінцівки. Функція пропуску Ren'Py дозволяє мені легко це зробити, пропускаючи текст, який я вже бачила."

# game/tutorial_playing.rpy:57
translate ukrainian tutorial_playing_93f7b8f9:

    # e "I can skip a few lines by holding down Control, or I can toggle skip mode by clicking the skip button at the bottom of the screen."
    e "Я можу пропустити кілька рядків, утримуючи Control, або я можу перемкнути режим пропуску, натиснувши кнопку пропуску внизу екрана."

# game/tutorial_playing.rpy:59
translate ukrainian tutorial_playing_d3553fbe:

    # e "By default, we only skip read text, so this won't do anything the first time through the game."
    e "За замовчуванням ми пропускаємо лише прочитаний текст, тому це нічого не дасть під час першого проходження гри."

# game/tutorial_playing.rpy:61
translate ukrainian tutorial_playing_fc0cac03:

    # e "Clicking the auto button toggles auto-forward mode, which makes the game advance without you clicking."
    e "Натискання кнопки 'Авто' перемикає режим автоматичної перемотки вперед, завдяки чому гра просувається без вас."

# game/tutorial_playing.rpy:63
translate ukrainian tutorial_playing_14e1c854:

    # e "The Q.Save and Q.Load buttons provide a single-click way to make a save, and a fast way to load that save again."
    e "Кнопки 'Ш.Збереження' та 'Ш.Завантаження' дозволяють одним клацанням зробити збереження та швидко завантажити це збереження знову."

# game/tutorial_playing.rpy:65
translate ukrainian tutorial_playing_5d6e4c0f:

    # e "Pressing the 's' key saves a screenshot to disk, so I can upload pictures of the game to websites like {a=https://www.renpy.org}renpy.org{/a}."
    e "Натискання клавіші 's' зберігає знімок екрана на диск, тож я можу завантажувати зображення гри на такі сайти, як {a=https://www.renpy.org}renpy.org{/a}."

# game/tutorial_playing.rpy:67
translate ukrainian tutorial_playing_73f3cfec:

    # e "The history button displays a history of read text - but you can also use rollback, which is usually better."
    e "Кнопка 'Історія' відображає історію прочитаного тексту, але ви також можете скористатися відкатом, що зазвичай краще."

# game/tutorial_playing.rpy:71
translate ukrainian tutorial_playing_f065a34c:

    # e "Would you like to hear about rollback?" nointeract
    e "Хочете почути про відкат?" nointeract

# game/tutorial_playing.rpy:84
translate ukrainian tutorial_rollback_a520091b:

    # e "You can invoke a rollback by clicking the 'Back' button, scrolling the mouse wheel up, or by pushing the page up key. That'll bring you back to the previous screen."
    e "Ви можете викликати відкат, натиснувши кнопку 'Назад', прокрутивши коліщатко миші вгору або натиснувши клавішу 'page up'. Це поверне вас до попереднього екрана."

# game/tutorial_playing.rpy:86
translate ukrainian tutorial_rollback_041be71b:

    # e "While at a previous screen, you can roll forward by scrolling the mouse wheel down, or pushing the page down key."
    e "Перебуваючи на попередньому екрані, ви можете перегортати вперед, прокручуючи коліщатко миші вниз або натискаючи клавішу 'page down'."

# game/tutorial_playing.rpy:88
translate ukrainian tutorial_rollback_4b903465:

    # e "Rolling forward through a menu will make the same choice you did last time. But you don't have to do that - Ren'Py's rollback system allows you to make a different choice."
    e "Прокручування вперед по меню зробить той самий вибір, який ви робили минулого разу. Але ви не повинні цього робити - система відкату Ren'Py дозволяє зробити інший вибір."

# game/tutorial_playing.rpy:90
translate ukrainian tutorial_rollback_a4633f53:

    # e "You can try it by rolling back through the last menu, and saying 'No'."
    e "Ви можете спробувати це, повернувшись до останнього меню та сказавши 'Ні'."

# game/tutorial_playing.rpy:92
translate ukrainian tutorial_rollback_5b73f822:

    # e "Click back a few times, press page up, or scroll up the mouse wheel."
    e "Щоб повернутися назад кілька разів, натисніть 'page up' або прокрутіть коліщатко миші."

# game/tutorial_playing.rpy:96
translate ukrainian tutorial_rollback_de0b6f5a:

    # e "Well, are you going to try it?"
    e "Ну що, ви збираєтеся спробувати?"

# game/tutorial_playing.rpy:98
translate ukrainian tutorial_rollback_6bbdedaa:

    # e "Your loss."
    e "Ви програли."

# game/tutorial_playing.rpy:100
translate ukrainian tutorial_rollback_dce979d4:

    # e "Moving on."
    e "Живіть."

# game/tutorial_playing.rpy:106
translate ukrainian tutorial_rollback_done_6564cd32:

    # e "By allowing Ren'Py to take care of out-of-game issues like loading and saving, you can focus on making your game, while still giving players the experience they've come to expect when playing visual novels."
    e "Дозволивши Ren'Py вирішувати позаігрові проблеми, як-от завантаження та збереження, ви можете зосередитися на створенні своєї гри, водночас даючи гравцям той досвід, якого вони звикли очікувати, граючи у візуальні романи."

