﻿
# game/indepth_style.rpy:40
translate ukrainian new_gui_17a0326e:

    # e "When you create a new project, <PERSON><PERSON><PERSON><PERSON> will automatically create a GUI - a Graphical User Interface - for it."
    e "Коли ви створюєте новий проєкт, Ren'Py автоматично створить для нього GUI - графічний інтерфейс користувача."

# game/indepth_style.rpy:42
translate ukrainian new_gui_12c814ed:

    # e "It defines the look of both in-game interface, like this text box, and out-of-game interface like the main and game menus."
    e "Він визначає зовнішній вигляд інтерфейсу гри, як-от це текстове поле, і позаігрового інтерфейсу, як-от головне та ігрове меню."

# game/indepth_style.rpy:44
translate ukrainian new_gui_0a2a73bb:

    # e "The default GUI is meant to be nice enough for a simple project. With a few small changes, it's what you're seeing in this game."
    e "Графічний інтерфейс за замовчуванням має бути достатньо зручним для простого проєкту. З кількома невеликими змінами, це те, що ви бачите в цій грі."

# game/indepth_style.rpy:46
translate ukrainian new_gui_22adf68e:

    # e "The GUI is also meant to be easy for an intermediate creator to customize. Customizing the GUI consists of changing the image files in the gui directory, and changing variables in gui.rpy."
    e "Графічний інтерфейс також призначений для легкого налаштування для творця середнього рівня. Налаштування GUI складається зі зміни файлів зображень у папці gui та змінення змінних у gui.rpy."

# game/indepth_style.rpy:48
translate ukrainian new_gui_da21de30:

    # e "At the same time, even when customized, the default GUI might be too recognizable for an extremely polished game. That's why we've made it easy to totally replace."
    e "У той же час, навіть якщо налаштувати, графічний інтерфейс за замовчуванням може бути занадто впізнаваним для надзвичайно відшліфованої гри. Ось чому ми спростили повну заміну."

# game/indepth_style.rpy:50
translate ukrainian new_gui_45765574:

    # e "We've put an extensive guide to customizing the GUI on the Ren'Py website. So if you want to learn more, visit the {a=https://www.renpy.org/doc/html/gui.html}GUI customization guide{/a}."
    e "Ми розмістили розширений посібник із налаштування графічного інтерфейсу користувача на сайті Ren'Py. Тому, якщо ви хочете дізнатися більше, відвідайте {a=https://www.renpy.org/doc/html/gui.html}посібник із налаштування GUI{/a}."

# game/indepth_style.rpy:58
translate ukrainian styles_fa345a38:

    # e "Ren'Py has a powerful style system that controls what displayables look like."
    e "Ren'Py має потужну систему стилів, яка контролює, як виглядають відображувані елементи."

# game/indepth_style.rpy:60
translate ukrainian styles_6189ee12:

    # e "While the default GUI uses variables to provide styles with sensible defaults, if you're replacing the GUI or creating your own screens, you'll need to learn about styles yourself."
    e "Хоча графічний інтерфейс за замовчуванням використовує змінні для надання стилів із розумними значеннями за замовчуванням."
    e "Якщо ви замінюєте графічний інтерфейс користувача або створюєте власні екрани, вам доведеться дізнатися про стилі самостійно."

# game/indepth_style.rpy:68
translate ukrainian styles_menu_a4a6913e:

    # e "What would you like to know about styles?" nointeract
    e "Що б ви хотіли знати про стилі?" nointeract

# game/indepth_style.rpy:98
translate ukrainian style_basics_9a79ef89:

    # e "Styles let a displayable look different from game to game, or even inside the same game."
    e "Стилі дозволяють відображуваному виглядати по-різному від гри до гри або навіть в одній грі."

# game/indepth_style.rpy:103
translate ukrainian style_basics_48777f2c:

    # e "Both of these buttons use the same displayables. But since different styles have been applied, the buttons look different from each other."
    e "Обидві ці кнопки використовують однакові елементи відображення. Але оскільки були застосовані різні стилі, кнопки виглядають по-різному."

# game/indepth_style.rpy:108
translate ukrainian style_basics_57704d8c:

    # e "Styles are a combination of information from four different places."
    e "Стилі — це комбінація інформації з чотирьох різних місць."

# game/indepth_style.rpy:121
translate ukrainian style_basics_144731f6:

    # e "The first place Ren'Py can get style information from is part of a screen. Each displayable created by a screen can take a style name and style properties."
    e "Перше місце, звідки Ren'Py може отримати інформацію про стиль, це частина екрана. Кожен відображуваний елемент, створений екраном, може приймати назву стилю та властивості стилю."

# game/indepth_style.rpy:138
translate ukrainian style_basics_67e48162:

    # e "When a screen displayable contains text, style properties prefixed with text_ apply to that text."
    e "Якщо відображуваний екран містить текст, властивості стилю з префіксом text_ застосовуються до цього тексту."

# game/indepth_style.rpy:151
translate ukrainian style_basics_03516b4a:

    # e "The next is as part of a displayable created in an image statement. Style properties are just arguments to the displayable."
    e "Наступне — як частина відображуваного, створеного в операторі image. Властивості стилю є лише аргументами для відображуваного."

# game/indepth_style.rpy:160
translate ukrainian style_basics_ccc0d1ca:

    # egreen "Style properties can also be given as arguments when defining a character."
    egreen "Властивості стилю також можна надавати як аргументи під час визначення символу."

# game/indepth_style.rpy:162
translate ukrainian style_basics_013ab314:

    # egreen "Arguments beginning with who_ are style properties applied to the character's name, while those beginning with what_ are applied to the character's dialogue."
    egreen "Аргументи, що починаються з who_, є властивостями стилю, застосованими до імені персонажа, а ті, що починаються з what_, застосовуються до діалогу персонажа."

# game/indepth_style.rpy:164
translate ukrainian style_basics_dbe80939:

    # egreen "Style properties that don't have a prefix are also applied to the character's name."
    egreen "Властивості стилю, які не мають префікса, також застосовуються до імені персонажа."

# game/indepth_style.rpy:174
translate ukrainian style_basics_ac6a8414:

    # e "Finally, there is the style statement, which creates or changes a named style. By giving Text the style argument, we tell it to use the blue_text style."
    e "І нарешті, є оператор style, який створює або змінює названий стиль. Надаючи Text аргумент style, ми повідомляємо йому використовувати стиль blue_text."

# game/indepth_style.rpy:180
translate ukrainian style_basics_3d9bdff7:

    # e "A style property can inherit from a parent. If a style property is not given in a style, it comes from the parent of that style."
    e "Властивість стилю може успадковуватися від батьківського. Якщо властивість стилю не задано в стилі, вона походить від батьківського елемента цього стилю."

# game/indepth_style.rpy:182
translate ukrainian style_basics_49c5fbfe:

    # e "By default the parent of the style has the same name, with the prefix up to the first underscore removed. If the style does not have an underscore in its name, 'default' is used."
    e "За замовчуванням батьківський стиль має таку саму назву, з вилученим префіксом до першого підкреслення. Якщо стиль не має підкреслення в назві, використовується 'default'."

# game/indepth_style.rpy:184
translate ukrainian style_basics_6ab170a3:

    # e "For example, blue_text inherits from text, which in turn inherits from default. The default style defines all properties, so it doesn't inherit from anything."
    e "Наприклад, blue_text успадковується від text, який, у свою чергу, успадковується від default. Стиль за замовчуванням визначає всі властивості, тому він нічого не успадковує."

# game/indepth_style.rpy:190
translate ukrainian style_basics_f78117a7:

    # e "The parent can be explicitly changed by giving the style statement an 'is' clause. In this case, we're explictly setting the style to the parent of text."
    e "Батьківський елемент можна явно змінити, додавши оператору стилю речення 'is'. У цьому випадку ми явно встановлюємо стиль для батьківського тексту."

# game/indepth_style.rpy:194
translate ukrainian style_basics_6007040b:

    # e "Each displayable has a default style name. By default, it's usually the lower-case displayable name, like 'text' for Text, or 'button' for buttons."
    e "Кожен відображуваний має назву стилю за замовчуванням. За замовчуванням це зазвичай ім’я для відображення в нижньому регістрі, наприклад 'text' для тексту або 'button' для кнопок."

# game/indepth_style.rpy:196
translate ukrainian style_basics_35db9a05:

    # e "In a screen, a displayable can be given the style_prefix property to give a prefix for that displayable and its children." id style_basics_35db9a05
    e "На екрані відображуваному елементу можна надати властивість style_prefix, щоб надати префікс для цього відображуваного елемента та його дочірніх елементів." id style_basics_35db9a05

# game/indepth_style.rpy:198
translate ukrainian style_basics_422a87f7:

    # e "For example, a text displayable with a style_prefix of 'help' will be given the style 'help_text'."
    e "Наприклад, тексту, який можна відобразити з style_prefix 'help', буде надано стиль 'help_text'."

# game/indepth_style.rpy:200
translate ukrainian style_basics_bad2e207:

    # e "Lastly, when a displayable is a button, or inside a button, it can take style prefixes."
    e "І на кінець, коли відображуваний елемент є кнопкою або всередині кнопки, він може мати префікси стилю."

# game/indepth_style.rpy:202
translate ukrainian style_basics_22ed20a1:

    # e "The prefixes idle_, hover_, and insensitive_ are used when the button is unfocused, focused, and unfocusable."
    e "Префікси idle _, hover _ і insensitive _ використовуються, коли кнопка не сфокусована, сфокусована або не фокусується."

# game/indepth_style.rpy:204
translate ukrainian style_basics_7a58037e:

    # e "These can be preceded by selected_ to change how the button looks when it represents a selected value or screen."
    e "Їм може передувати selected_, щоб змінити вигляд кнопки, коли вона представляє вибране значення або екран."

# game/indepth_style.rpy:233
translate ukrainian style_basics_0cdcb8c3:

    # e "This screen shows the style prefixes in action. You can click on a button to select it, or click outside to advance."
    e ""

# game/indepth_style.rpy:240
translate ukrainian style_basics_aed05094:

    # e "Those are the basics of styles. If GUI customization isn't enough for you, styles let you customize just about everything in Ren'Py."
    e "Це основи стилів. Якщо вам недостатньо налаштувати графічний інтерфейс, стилі дозволяють налаштувати майже все в Ren'Py."

# game/indepth_style.rpy:253
translate ukrainian style_general_81f3c8ff:

    # e "The first group of style properties that we'll go over are the general style properties. These work with every displayable, or at least many different ones."
    e "Перша група властивостей стилю, яку ми розглянемо, це загальні властивості стилю. Вони працюють з кожним відображуваним або принаймні з багатьма різними."

# game/indepth_style.rpy:264
translate ukrainian style_general_a8d99699:

    # e "Every displayable takes the position properties, which control where it can be placed on screen. Since I've already mentioned them, I won't repeat them here."
    e "Кожен відображуваний має властивості позиції, які визначають, де його можна розмістити на екрані. Оскільки я вже згадувала їх, я не буду повторювати їх тут."

# game/indepth_style.rpy:275
translate ukrainian style_general_58d4a18f:

    # e "The xmaximum and ymaximum properties set the maximum width and height of the displayable, respectively. This will cause Ren'Py to shrink things, if possible."
    e "Властивості xmaximum та ymaximum встановлюють максимальну ширину та висоту відображуваного, відповідно. Це змусить Ren'Py зменшити речі, якщо це можливо."

# game/indepth_style.rpy:277
translate ukrainian style_general_cae9a39f:

    # e "Sometimes, the shrunken size will be smaller than the size given by xmaximum and ymaximum."
    e "Іноді зменшений розмір буде меншим за розмір, визначений xmaximum та ymaximum."

# game/indepth_style.rpy:279
translate ukrainian style_general_5928c24e:

    # e "Similarly, the xminimum and yminimum properties set the minimum width and height. If the displayable is smaller, Ren'Py will try to make it bigger."
    e "Подібним чином властивості xminimum і yminimum встановлюють мінімальну ширину та висоту. Якщо відображуваний менший, Ren'Py спробує зробити його більшим."

# game/indepth_style.rpy:289
translate ukrainian style_general_35a8ee5e:

    # e "The xsize and ysize properties set the minimum and maximum size to the same value, fixing the size."
    e "Властивості xsize та ysize встановлюють мінімальний і максимальний розмір однаковими значеннями, фіксуючи розмір."

# game/indepth_style.rpy:291
translate ukrainian style_general_fcfb0640:

    # e "These only works for displayables than can be resized. Some displayables, like images, can't be made bigger or smaller."
    e "Це працює лише для відображуваних елементів, розмір яких можна змінити. Деякі відображувані елементи, наприклад зображення, не можна збільшити або зменшити."

# game/indepth_style.rpy:299
translate ukrainian style_general_cd5cc97c:

    # e "The area property takes a tuple - a parenthesis bounded list of four items. The first two give the position, and the second two the size."
    e "Властивість area приймає кортеж - обмежений круглими дужками список із чотирьох елементів. Перші два дають положення, а другі два розмір."

# game/indepth_style.rpy:308
translate ukrainian style_general_e5a58f0b:

    # e "Finally, the alt property changes the text used by self-voicing for the hearing impaired."
    e "І нарешті, властивість alt змінює текст, який використовується самоозвучуванням для людей із вадами слуху."

# game/indepth_style.rpy:335
translate ukrainian style_text_fe457b8f:

    # e "The text style properties apply to text and input displayables."
    e "Властивості text style застосовуються до тексту та відображуваних елементів введення."

# game/indepth_style.rpy:337
translate ukrainian style_text_7ab53f03:

    # e "Text displayables can be created implicitly or explicitly. For example, a textbutton creates a text displayable with a style ending in button_text."
    e "Відображувані текст елементи можна створювати неявно або явно. Наприклад, textbutton створює текст для відображення зі стилем, що закінчується на button_text."

# game/indepth_style.rpy:339
translate ukrainian style_text_6dd42a57:

    # e "These can also be set in gui.rpy by changing or defining variables with names like gui.button_text_size."
    e "Їх також можна встановити в gui.rpy, змінивши або визначивши змінні з такими іменами, як gui.button_text_size."

# game/indepth_style.rpy:347
translate ukrainian style_text_c689130e:

    # e "The bold style property makes the text bold. This can be done using an algorithm, rather than a different version of the font."
    e "Властивість bold style робить текст жирним. Це можна зробити за допомогою алгоритму, а не іншої версії шрифту."

# game/indepth_style.rpy:355
translate ukrainian style_text_3420bfe4:

    # e "The color property changes the color of the text. It takes hex color codes, just like everything else in Ren'Py."
    e "Властивість color змінює колір тексту. Він приймає шістнадцяткові коди кольорів, як і все інше в Ren'Py."

# game/indepth_style.rpy:363
translate ukrainian style_text_14bd6327:

    # e "The first_indent style property determines how far the first line is indented."
    e "Властивість стилю first_indent визначає, наскільки великий відступ першого рядка."

# game/indepth_style.rpy:371
translate ukrainian style_text_779ac517:

    # e "The font style property changes the font the text uses. Ren'Py takes TrueType and OpenType fonts, and you'll have to include the font file as part of your visual novel."
    e "Властивість стилю font змінює шрифт, який використовує текст. Ren'Py використовує шрифти TrueType і OpenType, і вам доведеться включити файл шрифту як частину вашої візуального роману."

# game/indepth_style.rpy:379
translate ukrainian style_text_917e2bca:

    # e "The size property changes the size of the text."
    e "Властивість size змінює розмір тексту."

# game/indepth_style.rpy:388
translate ukrainian style_text_1a46cd43:

    # e "The italic property makes the text italic. Again, this is better done with a font, but for short amounts of text Ren'Py can do it for you."
    e "Властивість italic робить текст курсивом. Знову ж таки, це краще зробити за допомогою шрифту, але Ren'Py може зробити це за короткі тексти."

# game/indepth_style.rpy:397
translate ukrainian style_text_472f382d:

    # e "The justify property makes the text justified, lining all but the last line up on the left and the right side."
    e "Властивість justify вирівнює текст, вирівнюючи всі рядки, крім останнього, зліва та справа."

# game/indepth_style.rpy:405
translate ukrainian style_text_87b075f8:

    # e "The kerning property kerns the text. When it's negative, characters are closer together. When positive, characters are farther apart."
    e "Властивість kerning керує текст. Коли він негативний, персонажі ближче один до одного. Коли позитивні, персонажі розташовані далі один від одного."

# game/indepth_style.rpy:415
translate ukrainian style_text_fe7dec14:

    # e "The line_leading and line_spacing properties put spacing before each line, and between lines, respectively."
    e "Властивості line_leading та line_spacing розміщують інтервал перед кожним рядком і між рядками відповідно."

# game/indepth_style.rpy:424
translate ukrainian style_text_aee9277a:

    # e "The outlines property puts outlines around text. This takes a list of tuples, which is a bit complicated."
    e "Властивість outlines розміщує контури навколо тексту. Тут потрібен список кортежів, який є трохи складнішим."

# game/indepth_style.rpy:426
translate ukrainian style_text_b4c5190f:

    # e "But if you ignore the brackets and parenthesis, you have the width of the outline, the color, and then horizontal and vertical offsets."
    e "Але якщо ви ігноруєте дужки та круглі дужки, у вас є ширина контуру, колір, а потім горизонтальні та вертикальні зміщення."

# game/indepth_style.rpy:434
translate ukrainian style_text_5a0c2c02:

    # e "The rest_indent property controls the indentation of lines after the first one."
    e "Властивість rest_indent керує відступом рядків після першого."

# game/indepth_style.rpy:443
translate ukrainian style_text_430c1959:

    # e "The textalign property controls the positioning of multiple lines of text inside the text displayable. For example, 0.5 means centered."
    e "Властивість textalign керує розташуванням кількох рядків тексту в тексті, що відображається. Наприклад, 0,5 означає по центру."

# game/indepth_style.rpy:445
translate ukrainian style_text_19aa0833:

    # e "It doesn't change the position of the text displayable itself. For that, you'll often want to set the textalign and xalign to the same value."
    e "Це не змінює позицію самого відображуваного тексту. Для цього вам часто потрібно встановити те саме значення для textalign і xalign."

# game/indepth_style.rpy:455
translate ukrainian style_text_efc3c392:

    # e "When both textalign and xalign are set to 1.0, the text is properly right-justified."
    e "Якщо як textalign, так і xalign встановлено значення 1.0, текст правильно вирівнюється по правому краю."

# game/indepth_style.rpy:464
translate ukrainian style_text_43be63b9:

    # e "The underline property underlines the text."
    e "Властивість underline підкреслює текст."

# game/indepth_style.rpy:471
translate ukrainian style_text_343f6d34:

    # e "Those are the most common text style properties, but not the only ones. Here are a few more that you might need in special circumstances."
    e "Це найпоширеніші властивості стилю тексту, але не єдині. Ось ще кілька, які можуть знадобитися в особливих обставинах."

# game/indepth_style.rpy:479
translate ukrainian style_text_e7204a95:

    # e "By default, text in Ren'Py is antialiased, to smooth the edges. The antialias property can turn that off, and make the text a little more jagged."
    e "За замовчуванням текст у Ren'Py згладжується, щоб згладити краї. Властивість antialias може вимкнути це та зробити текст трохи нерівнішим."

# game/indepth_style.rpy:487
translate ukrainian style_text_a5316e4c:

    # e "The adjust_spacing property is a very subtle one, that only matters when a player resizes the window. When True, characters will be shifted a bit so the Text has the same relative spacing."
    e "Властивість adjust_spacing є дуже тонкою, вона має значення лише тоді, коли гравець змінює розмір вікна. Якщо значення True, персонажі будуть трохи зміщені, щоб текст мав однаковий відносний інтервал."

# game/indepth_style.rpy:496
translate ukrainian style_text_605d4e4a:

    # e "When False, the text won't jump around as much. But it can be a little wider or narrower based on screen size."
    e "Коли False, текст не буде так сильно стрибати. Але він може бути трохи ширшим або вужчим залежно від розміру екрана."

# game/indepth_style.rpy:505
translate ukrainian style_text_acf8a0e1:

    # e "The layout property has a few special values that control where lines are broken. The 'nobreak' value disables line breaks entirely, making the text wider."
    e "Властивість layout має кілька спеціальних значень, які визначають, де лінії розриваються. Значення 'nobreak' повністю вимикає розриви рядків, роблячи текст ширшим."

# game/indepth_style.rpy:516
translate ukrainian style_text_785729cf:

    # e "When the layout property is set to 'subtitle', the line breaking algorithm is changed to try to make all lines even in length, as subtitles usually are."
    e "Якщо для властивості layout встановлено значення 'subtitle', алгоритм розриву рядків змінюється, щоб спробувати зробити всі рядки рівномірними за довжиною, як це зазвичай відбувається з субтитрами."

# game/indepth_style.rpy:524
translate ukrainian style_text_9c26f218:

    # e "The strikethrough property draws a line through the text. It seems pretty unlikely you'd want to use this one."
    e "Властивість strikethrough малює лінію через текст. Здається малоймовірним, що ви захочете скористатися цим."

# game/indepth_style.rpy:534
translate ukrainian style_text_c7229243:

    # e "The vertical style property places text in a vertical layout. It's meant for Asian languages with special fonts."
    e "Властивість vertical style розміщує текст у вертикальному макеті. Він призначений для азіатських мов зі спеціальними шрифтами."

# game/indepth_style.rpy:540
translate ukrainian style_text_724bd5e0:

    # e "And those are the text style properties. There might be a lot of them, but we want to give you a lot of control over how you present text to your players."
    e "І це властивості стилю тексту. Їх може бути багато, але ми хочемо дати вам повний контроль над тим, як ви подаєте текст своїм гравцям."

# game/indepth_style.rpy:580
translate ukrainian style_button_300b6af5:

    # e "Next up, we have the window and button style properties. These apply to windows like the text window at the bottom of this screen and frames like the ones we show examples in."
    e "Далі у нас є властивості стилю вікна та кнопки. Це стосується таких вікон, як текстове вікно внизу цього екрана, і рамків, подібних до тих, у яких ми показуємо приклади."

# game/indepth_style.rpy:582
translate ukrainian style_button_255a18e4:

    # e "These properties also apply to buttons, in-game and out-of-game. To Ren'Py, a button is a window you can click."
    e "Ці властивості також застосовуються до кнопок у грі та поза нею. Для Ren'Py кнопка - це вікно, яке можна натиснути."

# game/indepth_style.rpy:593
translate ukrainian style_button_9b53ce93:

    # e "I'll start off with this style, which everything will inherit from. To make our lives easier, it inherits from the default style, rather than the customized buttons in this game's GUI." id style_button_9b53ce93
    e "Почну з цього стилю, який успадкує все. Щоб полегшити наше життя, він успадковує стиль за замовчуванням, а не налаштовані кнопки в графічному інтерфейсі цієї гри." id style_button_9b53ce93

# game/indepth_style.rpy:595
translate ukrainian style_button_aece4a8c:

    # e "The first style property is the background property. It adds a background to a button or window. Since this is a button, idle and hover variants choose different backgrounds when focused." id style_button_aece4a8c
    e "Перша властивість стилю є властивість background. Він додає фон до кнопки чи вікна. Оскільки це кнопка, варіанти в режимі idle(очікування) та hover(навдення) вибирають різний фон під час фокусування." id style_button_aece4a8c

# game/indepth_style.rpy:597
translate ukrainian style_button_b969f04a:

    # e "We also center the two buttons, using the xalign position property."
    e ""

# game/indepth_style.rpy:601
translate ukrainian style_button_269ae069:

    # e "We've also customized the style of the button's text, using this style. It centers the text and makes it change color when hovered."
    e "Ми також налаштували стиль тексту кнопки, використовуючи цей стиль. Він центрує текст і змушує його змінювати колір при наведенні."

# game/indepth_style.rpy:612
translate ukrainian style_button_1009f3e1:

    # e "Without any padding around the text, the button looks odd. Ren'Py has padding properties that add space inside the button's background."
    e "Без відступу навколо тексту кнопка виглядає дивно. Ren'Py має властивості відступу, які додають простір всередині фону кнопки."

# game/indepth_style.rpy:621
translate ukrainian style_button_5bdfa45a:

    # e "More commonly used are the xpadding and ypadding style properties, which add the same padding to the left and right, or the top and bottom, respectively."
    e "Частіше використовуються властивості стилю xpadding і ypadding, які додають однакові відступи ліворуч і праворуч або зверху і знизу відповідно."

# game/indepth_style.rpy:629
translate ukrainian style_button_81283d42:

    # e "The margin style properties work the same way, except they add space outside the background. The full set exists: left_margin, right_margin, top_margin, bottom_margin, xmargin, and ymargin."
    e "Властивості стилю margin працюють так само, за винятком того, що вони додають простір поза фоном. Існує повний набір: left_margin, right_margin, top_margin, bottom_margin, xmargin і ymargin."

# game/indepth_style.rpy:638
translate ukrainian style_button_0b7aca6b:

    # e "The size_group style property takes a string. Ren'Py will make sure that all the windows or buttons with the same size_group string are the same size."
    e "Властивість стилю size_group приймає рядок. Ren'Py переконається, що всі вікна або кнопки з однаковим рядком size_group мають однаковий розмір."

# game/indepth_style.rpy:647
translate ukrainian style_button_4c6da7d9:

    # e "Alternatively, the xfill and yfill style properties make a button take up all available space in the horizontal or vertical directions."
    e "Крім того, властивості стилю xfill і yfill дозволяють кнопці займати весь доступний простір у горизонтальному або вертикальному напрямку."

# game/indepth_style.rpy:657
translate ukrainian style_button_fd5338b2:

    # e "The foreground property gives a displayable that is placed on top of the contents and background of the window or button."
    e "Властивість foreground надає відображуваний елемент, який розміщується поверх вмісту та фону вікна чи кнопки."

# game/indepth_style.rpy:659
translate ukrainian style_button_b8af697c:

    # e "One way to use it is to provide extra decorations to a button that's serving as a checkbox. Another would be to use it with a Frame to provide a glossy shine that overlays the button's contents."
    e "Один із способів його використання — додати додаткові прикраси до кнопки, яка служить прапорцем. Іншим варіантом було б використовувати його з рамкою, щоб забезпечити глянсовий блиск, який перекриває вміст кнопки."

# game/indepth_style.rpy:668
translate ukrainian style_button_c0b1b62e:

    # e "There are also a few style properties that only apply to buttons. The hover_sound and activate_sound properties play sound files when a button is focused and activated, respectively."
    e "Є також кілька властивостей стилю, які застосовуються лише до кнопок. Властивості hover_sound і activate_sound відтворюють звукові файли, коли кнопка фокусується і активується відповідно."

# game/indepth_style.rpy:677
translate ukrainian style_button_02fa647e:

    # e "Finally, the focus_mask property applies to partially transparent buttons. When it's set to True, only areas of the button that aren't transparent cause a button to focus."
    e "І нарешті, властивість focus_mask застосовується до частково прозорих кнопок. Якщо для нього встановлено значення True, лише непрозорі ділянки кнопки спричиняють фокусування кнопки."

# game/indepth_style.rpy:757
translate ukrainian style_bar_414d454a:

    # e "To demonstrate styles, let me first show two of the images we'll be using. This is the image we're using for parts of the bar that are empty."
    e "Щоб продемонструвати стилі, дозвольте мені спочатку показати два зображення, які ми будемо використовувати. Це зображення, яке ми використовуємо для порожніх частин панелі."

# game/indepth_style.rpy:761
translate ukrainian style_bar_9422b7b0:

    # e "And here's what we use for parts of the bar that are full."
    e "А ось що ми використовуємо для заповнених частин бару."

# game/indepth_style.rpy:773
translate ukrainian style_bar_8ae6a14b:

    # e "The left_bar and right_bar style properties, and their hover variants, give displayables for the left and right side of the bar. By default, the value is shown on the left."
    e "Властивості стилю left_bar і right_bar, а також їхні варіанти наведення курсора надають відображувані елементи для лівої та правої сторін панелі. За замовчуванням значення відображається зліва."

# game/indepth_style.rpy:775
translate ukrainian style_bar_7f0f50e5:

    # e "Also by default, both the left and right displayables are rendered at the full width of the bar, and then cropped to the appropriate size."
    e "Крім того, за замовчуванням ліворуч і праворуч відображувані елементи відображаються на всю ширину панелі, а потім обрізаються до відповідного розміру."

# game/indepth_style.rpy:777
translate ukrainian style_bar_9ef4f62f:

    # e "We give the bar the ysize property to set how tall it is. We could also give it xsize to choose how wide, but here it's limited by the width of the frame it's in."
    e "Ми надаємо панелі властивість ysize, щоб встановити її висоту. Ми також могли б надати йому xsize, щоб вибрати ширину, але тут він обмежений шириною рамки, у якій він знаходиться."

# game/indepth_style.rpy:790
translate ukrainian style_bar_d4c29710:

    # e "When the bar_invert style property is True, the bar value is displayed on the right side of the bar. The left_bar and right_bar displayables might also need to be swapped."
    e "Якщо властивість стилю bar_invert має значення True, значення панелі відображається праворуч від панелі. Відображувані елементи left_bar і right_bar також може знадобитися поміняти місцями."

# game/indepth_style.rpy:804
translate ukrainian style_bar_cca67222:

    # e "The bar_resizing style property causes the bar images to be resized to represent the value, rather than being rendered at full size and cropped."
    e "Властивість стилю bar_resizing спричиняє зміну розміру зображень панелі для відображення значення, а не відображення в повному розмірі та обрізання."

# game/indepth_style.rpy:817
translate ukrainian style_bar_7d361bac:

    # e "The thumb style property gives a thumb image, that's placed based on the bar's value. In the case of a scrollbar, it's resized if possible." id style_bar_7d361bac
    e "Властивість thumb style надає зображення великого пальця, яке розміщується на основі значення панелі. У випадку смуги прокрутки її розмір змінюється, якщо це можливо." id style_bar_7d361bac

# game/indepth_style.rpy:819
translate ukrainian style_bar_b6dfb61b:

    # e "Here, we use it with the base_bar style property, which sets both bar images to the same displayable."
    e "Тут ми використовуємо його з властивістю стилю base_bar, яка встановлює обидва зображення панелі на однакові для відображення."

# game/indepth_style.rpy:834
translate ukrainian style_bar_996466ad:

    # e "The left_gutter and right_gutter properties set a gutter on the left or right size of the bar. The gutter is space the bar can't be dragged into, that can be used for borders."
    e "Властивості left_gutter і right_gutter встановлюють ринву лівого або правого розміру панелі. Ринва — це простір, куди не можна перетягнути смужку, який можна використовувати для кордонів."

# game/indepth_style.rpy:849
translate ukrainian style_bar_fa41a83c:

    # e "The bar_vertical style property displays a vertically oriented bar. All of the other properties change names - left_bar becomes top_bar, while right_bar becomes bottom_bar."
    e "Властивість стилю bar_vertical відображає вертикально орієнтовану панель. Усі інші властивості змінюють назви - left_bar стає top_bar, тоді як right_bar стає bottom_bar."

# game/indepth_style.rpy:854
translate ukrainian style_bar_5d33c5dc:

    # e "Finally, there's one style we can't show here, and it's unscrollable. It controls what happens when a scrollbar can't be moved at all."
    e "І нарешті, є один стиль, який ми не можемо показати тут, і його не можна прокручувати. Він контролює, що відбувається, коли смугу прокрутки взагалі неможливо перемістити."

# game/indepth_style.rpy:856
translate ukrainian style_bar_e8e32280:

    # e "By default, it's shown. But if unscrollable is 'insensitive', the bar becomes insensitive. If it's 'hide', the bar is hidden, but still takes up space."
    e "За замовчуванням воно відображається. Але якщо unscrollable є 'insensitive', панель стає нечутливою. Якщо вибрано 'hide', панель прихована, але все одно займає місце."

# game/indepth_style.rpy:860
translate ukrainian style_bar_f1292000:

    # e "That's it for the bar properties. By using them, a creator can customize bars, scrollbars, and sliders."
    e "Ось і все щодо властивостей смуги. Використовуючи їх, автор може налаштувати смуги, смуги прокрутки та повзунки."

# game/indepth_style.rpy:959
translate ukrainian style_box_5fd535f4:

    # e "The hbox displayable is used to lay its children out horizontally. By default, there's no spacing between children, so they run together."
    e "hbox displayable використовується для горизонтального розташування дочірніх елементів. За замовчуванням між дочірніми елементами немає інтервалів, тому вони працюють разом."

# game/indepth_style.rpy:965
translate ukrainian style_box_0111e5dc:

    # e "Similarly, the vbox displayable is used to lay its children out vertically. Both support style properties that control placement."
    e "Подібним чином vbox displayable використовується для вертикального розміщення своїх дочірніх елементів. Обидва підтримують властивості стилю, які контролюють розміщення."

# game/indepth_style.rpy:970
translate ukrainian style_box_5a44717b:

    # e "To make the size of the box displayable obvious, I'll add a highlight to the box itself, and not the frame containing it."
    e "Щоб зробити видимим розмір вікна, я додаю виділення до самого вікна, а не до рамки, що його містить."

# game/indepth_style.rpy:978
translate ukrainian style_box_239e7a8f:

    # e "Boxes support the xfill and yfill style properties. These properties make a box expand to fill the available space, rather than the space of the largest child."
    e "Блоки підтримують властивості стилю xfill і yfill. Ці властивості роблять блок розширеним, щоб заповнити доступний простір, а не простір найбільшого дочірнього елемента."

# game/indepth_style.rpy:988
translate ukrainian style_box_e513c946:

    # e "The spacing style property takes a value in pixels, and adds that much spacing between each child of the box."
    e "Властивість стилю spacing приймає значення в пікселях і додає стільки інтервалу між кожним дочірнім елементом блоку."

# game/indepth_style.rpy:998
translate ukrainian style_box_6ae4f94d:

    # e "The first_spacing style property is similar, but it only adds space between the first and second children. This is useful when the first child is a title that needs different spacing."
    e "Властивість стилю first_spacing схожа, але вона лише додає пробіл між першим і другим дочірніми елементами. Це корисно, коли першим дочірнім елементом є заголовок, який потребує іншого інтервалу."

# game/indepth_style.rpy:1008
translate ukrainian style_box_0c518d9f:

    # e "The box_reverse style property reverses the order of entries in the box."
    e "Властивість стилю box_reverse змінює порядок записів у полі на протилежний."

# game/indepth_style.rpy:1021
translate ukrainian style_box_f73c1422:

    # e "We'll switch back to a horizontal box for our next example."
    e "Ми повернемося до горизонтального блока для нашого наступного прикладу."

# game/indepth_style.rpy:1031
translate ukrainian style_box_285592bb:

    # e "The box_wrap style property fills the box with children until it's full, then starts again on the next line."
    e "Властивість стилю box_wrap заповнює вікно дочірніми елементами, доки воно не заповниться, а потім починається знову з наступного рядка."

# game/indepth_style.rpy:1044
translate ukrainian style_box_a7637552:

    # e "Grids bring with them two more style properties. The xspacing and yspacing properties control spacing in the horizontal and vertical directions, respectively."
    e "Сітки приносять із собою ще дві властивості стилю. Властивості xspacing і yspacing контролюють відстань у горизонтальному та вертикальному напрямках відповідно."

# game/indepth_style.rpy:1051
translate ukrainian style_box_4006f74b:

    # e "Lastly, we have the fixed layout. The fixed layout usually expands to fill all space, and shows its children from back to front."
    e "І на кінець, у нас є fixed layout. Fixed layout зазвичай розширюється, щоб заповнити весь простір, і показує дочірні елементи ззаду наперед."

# game/indepth_style.rpy:1053
translate ukrainian style_box_4a2866f0:

    # e "But of course, we have some style properties that can change that."
    e "Але, звичайно, у нас є деякі властивості стилю, які можуть це змінити."

# game/indepth_style.rpy:1062
translate ukrainian style_box_66e042c4:

    # e "When the xfit style property is True, the fixed lays out all its children as if it was full size, and then shrinks in width to fit them. The yfit style works the same way, but in height."
    e "Коли властивість стилю xfit має значення True, fixed розміщує всі свої дочірні елементи так, ніби вони повного розміру, а потім зменшується в ширину. Стиль yfit працює так само, але у висоті."

# game/indepth_style.rpy:1070
translate ukrainian style_box_6a593b10:

    # e "The order_reverse style property changes the order in which the children are shown. Instead of back-to-front, they're displayed front-to-back."
    e "Властивість стилю order_reverse змінює порядок, у якому показано дочірні елементи. Замість тильного боку, вони відображаються спереду назад."

# game/indepth_style.rpy:1082
translate ukrainian style_inspector_21bc0709:

    # e "Sometimes it's hard to figure out what style is being used for a particular displayable. The displayable inspector can help with that."
    e "Іноді важко зрозуміти, який стиль використовується для певного відображуваного. Відображуваний інспектор може допомогти в цьому."

# game/indepth_style.rpy:1084
translate ukrainian style_inspector_243c50f0:

    # e "To use it, place the mouse over a portion of the Ren'Py user interface, and hit shift+I. That's I for inspector."
    e "Щоб скористатися ним, наведіть курсор миші на частину інтерфейсу користувача Ren'Py і натисніть shift+I. Це я для інспектора."

# game/indepth_style.rpy:1086
translate ukrainian style_inspector_bcbdc396:

    # e "Ren'Py will pop up a list of displayables the mouse is over. Next to each is the name of the style that displayable uses."
    e "Ren'Py відкриє список відображуваних елементів, над якими знаходиться миша. Біля кожного є назва стилю, який використовує відображуваний."

# game/indepth_style.rpy:1088
translate ukrainian style_inspector_d981e5c8:

    # e "You can click on the name of the style to see where it gets its properties from."
    e "Ви можете клацнути на назві стилю, щоб побачити, звідки він отримує свої властивості."

# game/indepth_style.rpy:1090
translate ukrainian style_inspector_ef46b86d:

    # e "By default, the inspector only shows interface elements like screens, and not images. Type shift+alt+I if you'd like to see images as well."
    e "За замовчуванням інспектор показує лише елементи інтерфейсу, як-от екрани, а не зображення. Введіть shift+alt+I, якщо ви також хочете побачити зображення."

# game/indepth_style.rpy:1092
translate ukrainian style_inspector_b59c6b69:

    # e "You can try the inspector right now, by hovering this text and hitting shift+I."
    e "Ви можете спробувати інспектор прямо зараз, навівши курсор миші на цей текст і натиснувши shift+I."

translate ukrainian strings:

    # game/indepth_style.rpy:20
    old "Button 1"
    new "Кнопка 1"

    # game/indepth_style.rpy:22
    old "Button 2"
    new "Кнопка 2"

    # game/indepth_style.rpy:66
    old "Style basics."
    new "Базовий стиль."

    # game/indepth_style.rpy:66
    old "General style properties."
    new "Загальні властивості стилю."

    # game/indepth_style.rpy:66
    old "Text style properties."
    new "Властивості стилю тексту."

    # game/indepth_style.rpy:66
    old "Window and Button style properties."
    new "Властивості стилю вікна та кнопки."

    # game/indepth_style.rpy:66
    old "Bar style properties."
    new "Властивості стилю смуги."

    # game/indepth_style.rpy:66
    old "Box, Grid, and Fixed style properties."
    new "Властивості стилю бокса, сітки та фіксатора."

    # game/indepth_style.rpy:66
    old "The Displayable Inspector."
    new "Відображуваний інспектор."

    # game/indepth_style.rpy:66
    old "That's all I want to know."
    new "Це все, що я хочу знати."

    # game/indepth_style.rpy:112
    old "This text is colored green."
    new "Цей текст позначений у зелений."

    # game/indepth_style.rpy:126
    old "Danger"
    new "Небезпека"

    # game/indepth_style.rpy:142
    old "This text is colored red."
    new "Цей текст позначений у червоний. "

    # game/indepth_style.rpy:170
    old "This text is colored blue."
    new "Цей текст позначений у синій."

    # game/indepth_style.rpy:248
    old "Orbiting Earth in the spaceship, I saw how beautiful our planet is.\n-Yuri Gagarin"
    new "Обертаючись навколо Землі на космічному кораблі, я побачив, яка прекрасна наша планета.\n-Юрій Гагарін"

    # game/indepth_style.rpy:303
    old "\"Orbiting Earth in the spaceship, I saw how beautiful our planet is.\" Said by Yuri Gagarin."
    new "\"Обертаючись навколо Землі в космічному кораблі, я побачив, яка прекрасна наша планета\", - сказав Юрій Гагарін."

    # game/indepth_style.rpy:326
    old "Vertical"
    new "Вертикальний"

    # game/indepth_style.rpy:329
    old "Far better it is to dare mighty things, to win glorious triumphs, even though checkered by failure, than to rank with those poor spirits who neither enjoy nor suffer much, because they live in the gray twilight that knows not victory nor defeat.\n\n-Theodore Roosevelt"
    new "Набагато краще наважуватися на могутні вчинки, здобувати славетні тріумфи, хоч і стримані невдачами, ніж бути в ряду з тими бідолашними духами, які не насолоджуються й не страждають багато, бо живуть у сірих сутінках, які не знають ні перемог, ні поразок.\n \n-Теодор Рузвельт"

    # game/indepth_style.rpy:561
    old "Top Choice"
    new "Верхній вибір"

    # game/indepth_style.rpy:566
    old "Bottom Choice"
    new "Нижній вибір"

    # game/indepth_style.rpy:877
    old "First Child"
    new "Перший дочірний"

    # game/indepth_style.rpy:878
    old "Second Child"
    new "Другий дочірний"

    # game/indepth_style.rpy:879
    old "Third Child"
    new "Третій дочірний"

    # game/indepth_style.rpy:882
    old "Fourth Child"
    new "Четвертий дочірний"

    # game/indepth_style.rpy:883
    old "Fifth Child"
    new "П'ятий дочірний"

    # game/indepth_style.rpy:884
    old "Sixth Child"
    new "Шостий дочірний"
